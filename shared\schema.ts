import { z } from "zod";

// Define types without database dependencies
export interface ShortenedLink {
  id: number;
  originalUrl: string;
  shortCode: string;
  clicks: number;
  createdAt: Date;
  userId?: number; // Optional - for user-owned links
  earnings?: number; // Earnings from this link
}

export interface User {
  id: number;
  username: string;
  password: string;
  telegramId?: string;
  mobileNumber?: string;
  withdrawalMethod?: string;
  totalEarnings: number;
  availableBalance: number;
  totalClicks: number;
  totalLinks: number;
  createdAt: Date;
  isActive: boolean;
}

export interface WithdrawalRequest {
  id: number;
  userId: number;
  amount: number;
  method: string;
  status: 'pending' | 'approved' | 'rejected';
  requestedAt: Date;
  processedAt?: Date;
}

// Zod schemas for validation
export const insertLinkSchema = z.object({
  originalUrl: z.string().url("Invalid URL format"),
});

export const insertUserSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const telegramRegistrationSchema = z.object({
  telegramId: z.string(),
  mobileNumber: z.string().min(10, "Valid mobile number required"),
  withdrawalMethod: z.string().min(1, "Withdrawal method required"),
});

export const withdrawalRequestSchema = z.object({
  amount: z.number().min(4, "Minimum withdrawal amount is $4"),
  method: z.string().min(1, "Withdrawal method required"),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertLink = z.infer<typeof insertLinkSchema>;
export type LoginUser = z.infer<typeof loginSchema>;
export type TelegramRegistration = z.infer<typeof telegramRegistrationSchema>;
export type WithdrawalRequestData = z.infer<typeof withdrawalRequestSchema>;

// Environment-based content types
export interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  date: string;
  readTime: string;
  imageUrl: string;
}

export interface Deal {
  id: string;
  title: string;
  currentPrice: string;
  originalPrice: string;
  discount: string;
  timeLeft: string;
  imageUrl: string;
}
