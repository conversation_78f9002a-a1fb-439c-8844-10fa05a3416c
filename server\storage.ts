import { type ShortenedLink, type InsertLink, type User, type WithdrawalRequest, type InsertUser, type TelegramRegistration } from "@shared/schema";
import TelegramBot from 'node-telegram-bot-api';
import bcrypt from 'bcryptjs';

export interface IStorage {
  // Link management
  createShortenedLink(link: InsertLink & { shortCode: string; userId?: number }): Promise<ShortenedLink>;
  getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined>;
  updateLinkClicks(shortCode: string, userId?: number): Promise<void>;
  getRecentLinks(): Promise<ShortenedLink[]>;
  getUserLinks(userId: number): Promise<ShortenedLink[]>;

  // User management
  createUser(userData: InsertUser): Promise<User>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>;
  updateUserFromTelegram(userId: number, telegramData: TelegramRegistration): Promise<User>;
  updateUserEarnings(userId: number, amount: number): Promise<void>;

  // Withdrawal management
  createWithdrawalRequest(userId: number, amount: number, method: string): Promise<WithdrawalRequest>;
  getUserWithdrawals(userId: number): Promise<WithdrawalRequest[]>;
}

export class TelegramStorage implements IStorage {
  private bot: TelegramBot;
  private channelId: string;
  private cache: Map<string, ShortenedLink> = new Map();
  private userCache: Map<number, User> = new Map();
  private usernameCache: Map<string, User> = new Map();
  private withdrawalCache: Map<number, WithdrawalRequest[]> = new Map();
  private initialized = false;

  // Earnings configuration
  private readonly EARNINGS_PER_CLICK = 0.007; // $0.007 per click
  private readonly MIN_WITHDRAWAL = 4; // $4 minimum withdrawal

  constructor() {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const channelId = process.env.TELEGRAM_CHANNEL_ID;

    if (!botToken || !channelId) {
      throw new Error('TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID must be set');
    }

    this.bot = new TelegramBot(botToken);
    this.channelId = channelId;
  }

  private async initializeCache(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load existing links from Telegram channel
      await this.loadLinksFromChannel();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize Telegram storage cache:', error);
      // Continue without cache - new links will still work
      this.initialized = true;
    }
  }

  private async loadLinksFromChannel(): Promise<void> {
    try {
      console.log('Loading existing links from Telegram channel...');

      // Since we can't easily retrieve chat history without special bot permissions,
      // we'll implement a recovery mechanism that works when links are accessed
      console.log('Cache will be built as links are accessed or created.');

      // Future enhancement: Store a backup of recent links in a simple text file
      // or implement a more sophisticated Telegram history retrieval system
    } catch (error) {
      console.error('Error loading links from channel:', error);
    }
  }

  async createShortenedLink(linkData: InsertLink & { shortCode: string; userId?: number }): Promise<ShortenedLink> {
    await this.initializeCache();

    const link: ShortenedLink = {
      id: Date.now(), // Use timestamp as ID
      originalUrl: linkData.originalUrl,
      shortCode: linkData.shortCode,
      clicks: 0,
      createdAt: new Date(),
      userId: linkData.userId,
      earnings: 0,
    };

    try {
      // Save to Telegram channel
      const message = JSON.stringify({
        type: 'shortened_link',
        data: link
      });

      await this.bot.sendMessage(this.channelId, message);

      // Cache the link
      this.cache.set(linkData.shortCode, link);

      console.log(`Saved link to Telegram: ${linkData.shortCode} -> ${linkData.originalUrl}`);
      return link;
    } catch (error) {
      console.error('Error saving link to Telegram:', error);
      throw new Error('Failed to save shortened link');
    }
  }

  async getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined> {
    await this.initializeCache();

    // First check cache
    const cachedLink = this.cache.get(shortCode);
    if (cachedLink) {
      return cachedLink;
    }

    // If not in cache, search Telegram channel history
    try {
      console.log(`Searching Telegram history for shortCode: ${shortCode}`);

      // Search through recent messages in the channel
      // We'll search backwards from the latest messages
      let offset = 0;
      const limit = 50; // Check last 50 messages at a time
      const maxSearchMessages = 200; // Don't search more than 200 messages

      while (offset < maxSearchMessages) {
        try {
          // Get chat history (this requires the bot to be admin with message history access)
          const chatId = parseInt(this.channelId);

          // Alternative approach: Use a simple search through stored message IDs
          // For now, we'll implement a basic search that works with the bot's limitations

          // Since we can't easily get chat history without special permissions,
          // let's implement a fallback that logs the issue and suggests manual recovery
          console.log(`Cannot search Telegram history automatically. Link ${shortCode} exists in channel but requires manual recovery.`);
          console.log(`Check your Telegram channel ${this.channelId} for the link data.`);

          return undefined;
        } catch (searchError) {
          console.error('Error in Telegram search:', searchError);
          break;
        }
      }

      return undefined;
    } catch (error) {
      console.error('Error searching Telegram history:', error);
      return undefined;
    }
  }

  async updateLinkClicks(shortCode: string, userId?: number): Promise<void> {
    await this.initializeCache();

    const link = this.cache.get(shortCode);
    if (link) {
      link.clicks += 1;

      // Calculate earnings if this is a user's link
      if (link.userId && userId) {
        const earnings = this.EARNINGS_PER_CLICK;
        link.earnings = (link.earnings || 0) + earnings;

        // Update user's total earnings
        await this.updateUserEarnings(link.userId, earnings);
      }

      try {
        // Send updated click count to Telegram
        const message = JSON.stringify({
          type: 'click_update',
          shortCode: shortCode,
          clicks: link.clicks,
          earnings: link.earnings || 0,
          userId: link.userId,
          timestamp: new Date().toISOString()
        });

        await this.bot.sendMessage(this.channelId, message);
      } catch (error) {
        console.error('Error updating clicks in Telegram:', error);
        // Don't throw error - click tracking is not critical
      }
    }
  }

  async getRecentLinks(): Promise<ShortenedLink[]> {
    await this.initializeCache();

    // Return recent links from cache, sorted by creation date
    const links = Array.from(this.cache.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    return links;
  }

  async getUserLinks(userId: number): Promise<ShortenedLink[]> {
    await this.initializeCache();

    // Return links owned by the user
    const links = Array.from(this.cache.values())
      .filter(link => link.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return links;
  }

  // User Management Methods
  async createUser(userData: InsertUser): Promise<User> {
    await this.initializeCache();

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    const user: User = {
      id: Date.now(),
      username: userData.username,
      password: hashedPassword,
      totalEarnings: 0,
      availableBalance: 0,
      totalClicks: 0,
      totalLinks: 0,
      createdAt: new Date(),
      isActive: true,
    };

    try {
      // Save to Telegram
      const message = JSON.stringify({
        type: 'user_created',
        data: user
      });

      await this.bot.sendMessage(this.channelId, message);

      // Cache the user
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);

      console.log(`Created user: ${user.username} (ID: ${user.id})`);
      return user;
    } catch (error) {
      console.error('Error creating user in Telegram:', error);
      throw new Error('Failed to create user');
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    await this.initializeCache();
    return this.usernameCache.get(username);
  }

  async getUserById(id: number): Promise<User | undefined> {
    await this.initializeCache();
    return this.userCache.get(id);
  }

  async updateUserPaymentDetails(userId: number, paymentData: any): Promise<User> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Update user with payment data
    user.fullName = paymentData.fullName;
    user.address = paymentData.address;
    user.phoneNumber = paymentData.phoneNumber;
    user.telegramUsername = paymentData.telegramUsername;
    user.trafficSource = paymentData.trafficSource;
    user.trafficSourceLinks = paymentData.trafficSourceLinks;
    user.paymentMethod = paymentData.paymentMethod;
    user.paymentDetails = paymentData.paymentDetails;
    user.isPaymentVerified = false; // Admin needs to verify

    try {
      // Save updated user to Telegram
      const message = JSON.stringify({
        type: 'payment_details_updated',
        userId: userId,
        username: user.username,
        paymentData: {
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          needsSupport: user.paymentMethod === 'other' ? true : false
        },
        adminNote: user.paymentMethod === 'other' ?
          '⚠️ USER SELECTED "OTHER" PAYMENT METHOD - CONTACT SUPPORT REQUIRED' :
          'Standard payment method selected',
        timestamp: new Date().toISOString()
      }, null, 2);

      await this.bot.sendMessage(this.channelId, message);

      // Update cache
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);

      console.log(`Updated payment details for user ${user.username}`);
      return user;
    } catch (error) {
      console.error('Error updating payment details in Telegram:', error);
      throw new Error('Failed to update payment details');
    }
  }

  async updateUserEarnings(userId: number, amount: number): Promise<void> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for earnings update`);
      return;
    }

    // Update earnings
    user.totalEarnings += amount;
    user.availableBalance += amount;
    user.totalClicks += 1;
  }

  // Temporary method for testing - manually set user balance
  async setUserBalanceForTesting(userId: number, balance: number): Promise<void> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for balance update`);
      return;
    }

    user.availableBalance = balance;
    user.totalEarnings = balance;
    console.log(`🧪 TEST: Set balance for user ${user.username} to $${balance.toFixed(3)}`);
  }

  async createWithdrawalRequest(userId: number, amount: number, method: string): Promise<WithdrawalRequest> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (user.availableBalance < amount) {
      throw new Error('Insufficient balance');
    }

    if (amount < this.MIN_WITHDRAWAL) {
      throw new Error(`Minimum withdrawal amount is $${this.MIN_WITHDRAWAL}`);
    }

    const withdrawal: WithdrawalRequest = {
      id: Date.now(),
      userId: userId,
      amount: amount,
      method: method,
      status: 'pending',
      requestedAt: new Date(),
    };

    try {
      // Save withdrawal request to Telegram with complete user details
      const message = JSON.stringify({
        type: 'withdrawal_request',
        withdrawalId: withdrawal.id,
        amount: withdrawal.amount,
        method: withdrawal.method,
        status: withdrawal.status,
        requestedAt: withdrawal.requestedAt,
        user: {
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          totalEarnings: user.totalEarnings,
          availableBalance: user.availableBalance,
          totalClicks: user.totalClicks,
          totalLinks: user.totalLinks,
          isPaymentVerified: user.isPaymentVerified
        }
      }, null, 2);

      await this.bot.sendMessage(this.channelId, message);

      // Update user balance (deduct the withdrawal amount)
      user.availableBalance -= amount;
      this.userCache.set(user.id, user);

      // Cache withdrawal
      const userWithdrawals = this.withdrawalCache.get(userId) || [];
      userWithdrawals.push(withdrawal);
      this.withdrawalCache.set(userId, userWithdrawals);

      console.log(`Withdrawal request created: $${amount} for user ${user.username}`);
      return withdrawal;
    } catch (error) {
      console.error('Error creating withdrawal request:', error);
      throw new Error('Failed to create withdrawal request');
    }
  }

  async getUserWithdrawals(userId: number): Promise<WithdrawalRequest[]> {
    await this.initializeCache();
    return this.withdrawalCache.get(userId) || [];
  }
}

// Lazy initialization to ensure environment variables are loaded
let _storage: TelegramStorage | null = null;

export const storage = {
  get instance(): TelegramStorage {
    if (!_storage) {
      _storage = new TelegramStorage();
    }
    return _storage;
  },

  // Proxy all link methods to the instance
  async createShortenedLink(link: InsertLink & { shortCode: string; userId?: number }) {
    return this.instance.createShortenedLink(link);
  },

  async getShortenedLink(shortCode: string) {
    return this.instance.getShortenedLink(shortCode);
  },

  async updateLinkClicks(shortCode: string, userId?: number) {
    return this.instance.updateLinkClicks(shortCode, userId);
  },

  async getRecentLinks() {
    return this.instance.getRecentLinks();
  },

  async getUserLinks(userId: number) {
    return this.instance.getUserLinks(userId);
  },

  // Proxy all user methods to the instance
  async createUser(userData: InsertUser) {
    return this.instance.createUser(userData);
  },

  async getUserByUsername(username: string) {
    return this.instance.getUserByUsername(username);
  },

  async getUserById(id: number) {
    return this.instance.getUserById(id);
  },

  async updateUserPaymentDetails(userId: number, paymentData: any) {
    return this.instance.updateUserPaymentDetails(userId, paymentData);
  },

  async updateUserEarnings(userId: number, amount: number) {
    return this.instance.updateUserEarnings(userId, amount);
  },

  // Proxy all withdrawal methods to the instance
  async createWithdrawalRequest(userId: number, amount: number, method: string) {
    return this.instance.createWithdrawalRequest(userId, amount, method);
  },

  async getUserWithdrawals(userId: number) {
    return this.instance.getUserWithdrawals(userId);
  },

  // Temporary testing method
  async setUserBalanceForTesting(userId: number, balance: number) {
    return this.instance.setUserBalanceForTesting(userId, balance);
  }
};
