import { type ShortenedLink, type InsertLink, type User, type WithdrawalRequest, type InsertUser, type TelegramRegistration } from "@shared/schema";
import TelegramBot from 'node-telegram-bot-api';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

export interface IStorage {
  // Link management
  createShortenedLink(link: InsertLink & { shortCode: string; userId?: number }): Promise<ShortenedLink>;
  getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined>;
  updateLinkClicks(shortCode: string, userId?: number): Promise<void>;
  getRecentLinks(): Promise<ShortenedLink[]>;
  getUserLinks(userId: number): Promise<ShortenedLink[]>;

  // User management
  createUser(userData: InsertUser): Promise<User>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>;
  updateUserFromTelegram(userId: number, telegramData: TelegramRegistration): Promise<User>;
  updateUserEarnings(userId: number, amount: number): Promise<void>;
  getAllUsers(): Promise<User[]>;
  getAllLinks(): Promise<ShortenedLink[]>;

  // Withdrawal management
  createWithdrawalRequest(userId: number, amount: number, method: string): Promise<WithdrawalRequest>;
  getUserWithdrawals(userId: number): Promise<WithdrawalRequest[]>;
  updateWithdrawalStatus(withdrawalId: number, status: string, adminUsername: string): Promise<void>;
  restoreUserBalance(userId: number, withdrawalId: number): Promise<void>;
}

export class TelegramStorage implements IStorage {
  private bot: TelegramBot;
  private channelId: string;
  private cache: Map<string, ShortenedLink> = new Map();
  private userCache: Map<number, User> = new Map();
  private usernameCache: Map<string, User> = new Map();
  private withdrawalCache: Map<number, WithdrawalRequest[]> = new Map();
  private initialized = false;

  // Earnings configuration
  private readonly EARNINGS_PER_CLICK = 0.0014; // $0.0014 per click (updated as requested)
  private readonly MIN_WITHDRAWAL = 4; // $4 minimum withdrawal

  // Backup file paths
  private readonly BACKUP_DIR = path.join(process.cwd(), 'data');
  private readonly USERS_BACKUP_FILE = path.join(this.BACKUP_DIR, 'users.json');
  private readonly LINKS_BACKUP_FILE = path.join(this.BACKUP_DIR, 'links.json');

  constructor() {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const channelId = process.env.TELEGRAM_CHANNEL_ID;

    if (!botToken || !channelId) {
      throw new Error('TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID must be set');
    }

    this.bot = new TelegramBot(botToken);
    this.channelId = channelId;

    // Ensure backup directory exists
    if (!fs.existsSync(this.BACKUP_DIR)) {
      fs.mkdirSync(this.BACKUP_DIR, { recursive: true });
    }
  }

  private async initializeCache(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load existing data from backup files first
      await this.loadFromBackup();
      // Then try to load from Telegram channel
      await this.loadLinksFromChannel();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize Telegram storage cache:', error);
      // Continue without cache - new links will still work
      this.initialized = true;
    }
  }

  private async loadLinksFromChannel(): Promise<void> {
    try {
      console.log('Loading existing data from Telegram channel...');

      // Try to get recent messages from the channel
      try {
        // Note: This requires the bot to have access to message history
        // In production, you might want to implement a different persistence strategy
        console.log('📂 Attempting to load cached data from Telegram...');

        // For now, we'll start with empty cache and build it as data is accessed
        // This ensures the system works even without message history access
        console.log('📂 Starting with empty cache - data will be rebuilt as accessed');

        // Future enhancement: Implement file-based backup or database persistence
        // to maintain data across server restarts
      } catch (channelError) {
        console.log('⚠️ Could not access channel history, starting fresh');
      }

      console.log('Cache initialization complete');
    } catch (error) {
      console.error('Error loading data from channel:', error);
    }
  }

  private async loadFromBackup(): Promise<void> {
    try {
      // Load users from backup
      if (fs.existsSync(this.USERS_BACKUP_FILE)) {
        const usersData = fs.readFileSync(this.USERS_BACKUP_FILE, 'utf8');
        const users: User[] = JSON.parse(usersData);

        for (const user of users) {
          // Convert date strings back to Date objects
          user.createdAt = new Date(user.createdAt);
          this.userCache.set(user.id, user);
          this.usernameCache.set(user.username, user);
        }

        console.log(`📂 Loaded ${users.length} users from backup`);
      }

      // Load links from backup
      if (fs.existsSync(this.LINKS_BACKUP_FILE)) {
        const linksData = fs.readFileSync(this.LINKS_BACKUP_FILE, 'utf8');
        const links: ShortenedLink[] = JSON.parse(linksData);

        for (const link of links) {
          // Convert date strings back to Date objects
          link.createdAt = new Date(link.createdAt);
          this.cache.set(link.shortCode, link);
        }

        console.log(`📂 Loaded ${links.length} links from backup`);
      }
    } catch (error) {
      console.log('⚠️ Could not load from backup files:', error.message);
    }
  }

  private async saveToBackup(): Promise<void> {
    try {
      // Save users to backup
      const users = Array.from(this.userCache.values());
      fs.writeFileSync(this.USERS_BACKUP_FILE, JSON.stringify(users, null, 2));

      // Save links to backup
      const links = Array.from(this.cache.values());
      fs.writeFileSync(this.LINKS_BACKUP_FILE, JSON.stringify(links, null, 2));

      console.log(`💾 Backup saved: ${users.length} users, ${links.length} links`);
    } catch (error) {
      console.error('⚠️ Failed to save backup:', error);
    }
  }

  async createShortenedLink(linkData: InsertLink & { shortCode: string; userId?: number }): Promise<ShortenedLink> {
    await this.initializeCache();

    const link: ShortenedLink = {
      id: Date.now(), // Use timestamp as ID
      originalUrl: linkData.originalUrl,
      shortCode: linkData.shortCode,
      clicks: 0,
      createdAt: new Date(),
      userId: linkData.userId,
      earnings: 0,
    };

    try {
      // Save to Telegram channel
      const message = JSON.stringify({
        type: 'shortened_link',
        data: link
      });

      await this.bot.sendMessage(this.channelId, message);

      // Cache the link
      this.cache.set(linkData.shortCode, link);

      // Save backup
      await this.saveToBackup();

      console.log(`Saved link to Telegram: ${linkData.shortCode} -> ${linkData.originalUrl}`);
      return link;
    } catch (error) {
      console.error('Error saving link to Telegram:', error);
      throw new Error('Failed to save shortened link');
    }
  }

  async getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined> {
    await this.initializeCache();

    // First check cache
    const cachedLink = this.cache.get(shortCode);
    if (cachedLink) {
      return cachedLink;
    }

    // If not in cache, search Telegram channel history
    try {
      console.log(`Searching Telegram history for shortCode: ${shortCode}`);

      // Search through recent messages in the channel
      // We'll search backwards from the latest messages
      let offset = 0;
      const limit = 50; // Check last 50 messages at a time
      const maxSearchMessages = 200; // Don't search more than 200 messages

      while (offset < maxSearchMessages) {
        try {
          // Get chat history (this requires the bot to be admin with message history access)
          const chatId = parseInt(this.channelId);

          // Alternative approach: Use a simple search through stored message IDs
          // For now, we'll implement a basic search that works with the bot's limitations

          // Since we can't easily get chat history without special permissions,
          // let's implement a fallback that logs the issue and suggests manual recovery
          console.log(`Cannot search Telegram history automatically. Link ${shortCode} exists in channel but requires manual recovery.`);
          console.log(`Check your Telegram channel ${this.channelId} for the link data.`);

          return undefined;
        } catch (searchError) {
          console.error('Error in Telegram search:', searchError);
          break;
        }
      }

      return undefined;
    } catch (error) {
      console.error('Error searching Telegram history:', error);
      return undefined;
    }
  }

  async updateLinkClicks(shortCode: string, userId?: number): Promise<void> {
    await this.initializeCache();

    const link = this.cache.get(shortCode);
    if (link) {
      link.clicks += 1;

      // Calculate earnings if this is a user's link
      if (link.userId && userId) {
        const earnings = this.EARNINGS_PER_CLICK;
        link.earnings = (link.earnings || 0) + earnings;

        // Update user's total earnings
        await this.updateUserEarnings(link.userId, earnings);
      }

      try {
        // Send updated click count to Telegram
        const message = JSON.stringify({
          type: 'click_update',
          shortCode: shortCode,
          clicks: link.clicks,
          earnings: link.earnings || 0,
          userId: link.userId,
          timestamp: new Date().toISOString()
        });

        await this.bot.sendMessage(this.channelId, message);
      } catch (error) {
        console.error('Error updating clicks in Telegram:', error);
        // Don't throw error - click tracking is not critical
      }
    }
  }

  async getRecentLinks(): Promise<ShortenedLink[]> {
    await this.initializeCache();

    // Return recent links from cache, sorted by creation date
    const links = Array.from(this.cache.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    return links;
  }

  async getUserLinks(userId: number): Promise<ShortenedLink[]> {
    await this.initializeCache();

    // Return links owned by the user
    const links = Array.from(this.cache.values())
      .filter(link => link.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return links;
  }

  // User Management Methods
  async createUser(userData: InsertUser): Promise<User> {
    await this.initializeCache();

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    const user: User = {
      id: Date.now(),
      username: userData.username,
      password: hashedPassword,
      totalEarnings: 0,
      availableBalance: 0,
      totalClicks: 0,
      totalLinks: 0,
      createdAt: new Date(),
      isActive: true,
    };

    try {
      // Save to Telegram
      const message = JSON.stringify({
        type: 'user_created',
        data: user
      });

      await this.bot.sendMessage(this.channelId, message);

      // Cache the user
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);

      // Save backup
      await this.saveToBackup();

      console.log(`Created user: ${user.username} (ID: ${user.id})`);
      return user;
    } catch (error) {
      console.error('Error creating user in Telegram:', error);
      throw new Error('Failed to create user');
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    await this.initializeCache();
    return this.usernameCache.get(username);
  }

  async getUserById(id: number): Promise<User | undefined> {
    await this.initializeCache();
    return this.userCache.get(id);
  }

  async updateUserPaymentDetails(userId: number, paymentData: any): Promise<User> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Update user with payment data
    user.fullName = paymentData.fullName;
    user.address = paymentData.address;
    user.phoneNumber = paymentData.phoneNumber;
    user.telegramUsername = paymentData.telegramUsername;
    user.trafficSource = paymentData.trafficSource;
    user.trafficSourceLinks = paymentData.trafficSourceLinks;
    user.paymentMethod = paymentData.paymentMethod;
    user.paymentDetails = paymentData.paymentDetails;
    user.isPaymentVerified = false; // Admin needs to verify

    try {
      // Save updated user to Telegram
      const message = JSON.stringify({
        type: 'payment_details_updated',
        userId: userId,
        username: user.username,
        paymentData: {
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          needsSupport: user.paymentMethod === 'other' ? true : false
        },
        adminNote: user.paymentMethod === 'other' ?
          '⚠️ USER SELECTED "OTHER" PAYMENT METHOD - CONTACT SUPPORT REQUIRED' :
          'Standard payment method selected',
        timestamp: new Date().toISOString()
      }, null, 2);

      await this.bot.sendMessage(this.channelId, message);

      // Update cache
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);

      console.log(`Updated payment details for user ${user.username}`);
      return user;
    } catch (error) {
      console.error('Error updating payment details in Telegram:', error);
      throw new Error('Failed to update payment details');
    }
  }

  async updateUserEarnings(userId: number, amount: number): Promise<void> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for earnings update`);
      return;
    }

    // Update earnings
    user.totalEarnings += amount;
    user.availableBalance += amount;
    user.totalClicks += 1;
  }

  // Temporary method for testing - manually set user balance
  async setUserBalanceForTesting(userId: number, balance: number): Promise<void> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for balance update`);
      return;
    }

    user.availableBalance = balance;
    user.totalEarnings = balance;
    console.log(`🧪 TEST: Set balance for user ${user.username} to $${balance.toFixed(3)}`);
  }

  async createWithdrawalRequest(userId: number, amount: number, method: string): Promise<WithdrawalRequest> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (amount > user.availableBalance) {
      throw new Error('Cannot request more than available balance');
    }

    if (amount <= 0) {
      throw new Error('Withdrawal amount must be greater than 0');
    }

    const withdrawal: WithdrawalRequest = {
      id: Date.now(),
      userId: userId,
      amount: amount,
      method: method,
      status: 'pending',
      requestedAt: new Date(),
    };

    try {
      // Get admin IDs from environment variable or use defaults
      const adminIdsEnv = process.env.TELEGRAM_ADMIN_IDS || '';
      const adminIds = adminIdsEnv ? adminIdsEnv.split(',').map(id => id.trim()) : [
        '*********',  // Default admin ID - replace with actual admin Telegram ID
        '*********'   // Add more admin IDs as needed
      ];

      console.log(`📋 Configured admin IDs: ${adminIds.join(', ')}`);

      // Create a neat, formatted withdrawal request message
      const paymentMethodEmoji = {
        'upi': '🇮🇳',
        'paypal': '💳',
        'bank': '🏦',
        'skrill': '💰',
        'other': '🔧',
        'not_set': '❓'
      }[method] || '💳';

      const hasPaymentDetails = user.fullName && user.paymentMethod && user.paymentDetails;
      const originalBalance = user.availableBalance + amount; // Balance before deduction
      const meetsMinimumBalance = originalBalance >= this.MIN_WITHDRAWAL;
      const canProcessPayment = meetsMinimumBalance && hasPaymentDetails;

      const formattedMessage = `
🚨 **WITHDRAWAL REQUEST** 🚨

💰 **Amount:** $${amount.toFixed(3)}
${paymentMethodEmoji} **Payment Method:** ${method === 'not_set' ? 'NOT SET' : method.toUpperCase()}

${!meetsMinimumBalance ? `⚠️ **BALANCE WARNING:** User balance ($${user.availableBalance.toFixed(3)}) is below minimum payout ($${this.MIN_WITHDRAWAL}.00)\n` : ''}

👤 **User Details:**
• **Name:** ${user.fullName || 'Not provided'}
• **Username:** @${user.username}
• **Telegram:** ${user.telegramUsername || 'Not provided'}
• **Phone:** ${user.phoneNumber || 'Not provided'}

📍 **Address:**
${user.address || 'Not provided'}

💳 **Payment Details:**
${user.paymentDetails || 'Not provided'}

📊 **Account Stats:**
• **Total Earnings:** $${user.totalEarnings.toFixed(3)}
• **Current Balance:** $${user.availableBalance.toFixed(3)}
• **Requested Amount:** $${amount.toFixed(3)}
• **Remaining After:** $${(user.availableBalance - amount).toFixed(3)}
• **Total Links:** ${user.totalLinks}
• **Total Clicks:** ${user.totalClicks}

🌐 **Traffic Sources:**
${user.trafficSource || 'Not provided'}

🔗 **Traffic Links:**
${user.trafficSourceLinks || 'Not provided'}

⏰ **Request Time:** ${withdrawal.requestedAt.toLocaleString()}
🆔 **Request ID:** ${withdrawal.id}

---
${canProcessPayment ?
  '**Status:** ✅ READY FOR PAYMENT\n**Admin:** User meets all requirements. Process payment immediately.' :
  !meetsMinimumBalance && !hasPaymentDetails ?
    '**Status:** ❌ CANNOT PROCESS\n**Admin:** User below minimum balance AND missing payment details.' :
    !meetsMinimumBalance ?
      '**Status:** ⏳ PENDING BALANCE\n**Admin:** User has payment details but balance below $4.00 minimum.' :
      '**Status:** ⚠️ INCOMPLETE DETAILS\n**Admin:** User meets balance requirement but missing payment details.'
}

**⚡ Admin Processing Checklist:**
• ${meetsMinimumBalance ? '✅' : '❌'} Original balance: $${originalBalance.toFixed(3)} ≥ $${this.MIN_WITHDRAWAL}.00
• ${hasPaymentDetails ? '✅' : '❌'} Payment details complete
• ${canProcessPayment ? '✅ PROCESS PAYMENT NOW' : '❌ REVIEW REQUIRED - Check payment policy'}

${!meetsMinimumBalance ? `\n🔔 **NOTE:** User was below $${this.MIN_WITHDRAWAL} minimum but money has been deducted. Admin decides payment policy.` : ''}
      `.trim();

      // Create admin action buttons
      const adminButtons = {
        inline_keyboard: [
          [
            {
              text: '✅ Payment Done',
              callback_data: `payment_done_${withdrawal.id}_${userId}`
            },
            {
              text: '❌ Reject Payment',
              callback_data: `payment_reject_${withdrawal.id}_${userId}`
            }
          ],
          [
            {
              text: '📞 Contact User',
              callback_data: `contact_user_${withdrawal.id}_${userId}`
            },
            {
              text: '🔍 View User Details',
              callback_data: `view_user_${withdrawal.id}_${userId}`
            }
          ]
        ]
      };

      // Send withdrawal request to each admin privately with buttons
      let adminNotified = false;
      for (const adminId of adminIds) {
        try {
          console.log(`📤 Sending withdrawal request to admin ${adminId}...`);
          await this.bot.sendMessage(adminId, formattedMessage, {
            reply_markup: adminButtons
          });
          console.log(`✅ Withdrawal request sent to admin ${adminId} with buttons`);
          adminNotified = true;
        } catch (adminError) {
          console.log(`⚠️ Failed to send to admin ${adminId}:`, adminError.message);
          // Try without markdown if parsing fails
          try {
            const simpleMessage = formattedMessage.replace(/\*\*/g, '').replace(/\*/g, '').replace(/`/g, '');
            await this.bot.sendMessage(adminId, simpleMessage, {
              reply_markup: adminButtons
            });
            console.log(`✅ Withdrawal request sent to admin ${adminId} (plain text)`);
            adminNotified = true;
          } catch (fallbackError) {
            console.log(`❌ Complete failure for admin ${adminId}:`, fallbackError.message);
          }
        }
      }

      // Also send a simple notification to the channel (without buttons)
      try {
        const channelNotification = `🚨 **NEW WITHDRAWAL REQUEST**\n\n` +
          `💰 Amount: $${amount.toFixed(3)}\n` +
          `👤 User: @${user.username}\n` +
          `🆔 Request ID: ${withdrawal.id}\n` +
          `⏰ Time: ${withdrawal.requestedAt.toLocaleString()}\n\n` +
          `📱 Admins have been notified privately with action buttons.`;

        await this.bot.sendMessage(this.channelId, channelNotification);
        console.log('✅ Channel notification sent');
      } catch (channelError) {
        console.log('⚠️ Failed to send channel notification:', channelError.message);
      }

      if (!adminNotified) {
        console.log('❌ No admins were notified! Please check admin IDs.');
      }

      // No need to pin since admins get private messages with buttons

      // Also save the raw data as a backup (in case needed for search)
      const backupData = JSON.stringify({
        type: 'withdrawal_request',
        withdrawalId: withdrawal.id,
        amount: withdrawal.amount,
        method: withdrawal.method,
        status: withdrawal.status,
        requestedAt: withdrawal.requestedAt,
        user: {
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          totalEarnings: user.totalEarnings,
          availableBalance: user.availableBalance,
          totalClicks: user.totalClicks,
          totalLinks: user.totalLinks
        }
      }, null, 2);

      // Send backup data as a separate message (for search functionality)
      await this.bot.sendMessage(this.channelId, `🔍 SEARCH_DATA: ${backupData}`);

      // Always deduct balance when withdrawal is requested (as requested by user)
      user.availableBalance -= amount;
      this.userCache.set(user.id, user);
      console.log(`💰 Balance deducted: $${amount} from user ${user.username} (new balance: $${user.availableBalance.toFixed(3)})`);

      // Note if user was below minimum for admin information
      const balanceBeforeDeduction = user.availableBalance + amount;
      const meetsMinimum = balanceBeforeDeduction >= this.MIN_WITHDRAWAL;
      if (!meetsMinimum) {
        console.log(`⚠️ Note: User ${user.username} was below minimum ($${balanceBeforeDeduction.toFixed(3)} < $${this.MIN_WITHDRAWAL}) but money still deducted as requested`);
      }

      // Cache withdrawal
      const userWithdrawals = this.withdrawalCache.get(userId) || [];
      userWithdrawals.push(withdrawal);
      this.withdrawalCache.set(userId, userWithdrawals);

      console.log(`Withdrawal request created: $${amount} for user ${user.username}`);
      return withdrawal;
    } catch (error) {
      console.error('Error creating withdrawal request:', error);
      throw new Error('Failed to create withdrawal request');
    }
  }

  async getUserWithdrawals(userId: number): Promise<WithdrawalRequest[]> {
    await this.initializeCache();
    return this.withdrawalCache.get(userId) || [];
  }

  async getAllUsers(): Promise<User[]> {
    await this.initializeCache();
    return Array.from(this.userCache.values());
  }

  async getAllLinks(): Promise<ShortenedLink[]> {
    await this.initializeCache();
    return Array.from(this.linkCache.values());
  }

  async updateWithdrawalStatus(withdrawalId: number, status: string, adminUsername: string): Promise<void> {
    await this.initializeCache();

    // Find and update withdrawal in cache
    for (const [userId, withdrawals] of this.withdrawalCache.entries()) {
      const withdrawal = withdrawals.find(w => w.id === withdrawalId);
      if (withdrawal) {
        withdrawal.status = status;
        withdrawal.processedBy = adminUsername;
        withdrawal.processedAt = new Date();

        // Save update to Telegram
        const updateMessage = JSON.stringify({
          type: 'withdrawal_status_update',
          withdrawalId: withdrawalId,
          userId: userId,
          status: status,
          processedBy: adminUsername,
          processedAt: new Date().toISOString()
        }, null, 2);

        await this.bot.sendMessage(this.channelId, `🔄 WITHDRAWAL UPDATE: ${updateMessage}`);
        console.log(`📝 Withdrawal ${withdrawalId} status updated to ${status} by ${adminUsername}`);
        break;
      }
    }
  }

  async restoreUserBalance(userId: number, withdrawalId: number): Promise<void> {
    await this.initializeCache();

    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Find the withdrawal to get the amount
    for (const withdrawals of this.withdrawalCache.values()) {
      const withdrawal = withdrawals.find(w => w.id === withdrawalId);
      if (withdrawal) {
        // Restore the balance
        user.availableBalance += withdrawal.amount;
        this.userCache.set(userId, user);

        // Log the restoration
        const restoreMessage = JSON.stringify({
          type: 'balance_restored',
          userId: userId,
          username: user.username,
          withdrawalId: withdrawalId,
          restoredAmount: withdrawal.amount,
          newBalance: user.availableBalance,
          restoredAt: new Date().toISOString()
        }, null, 2);

        await this.bot.sendMessage(this.channelId, `💰 BALANCE RESTORED: ${restoreMessage}`);
        console.log(`💰 Restored $${withdrawal.amount} to user ${user.username} (new balance: $${user.availableBalance.toFixed(3)})`);
        break;
      }
    }
  }
}

// Lazy initialization to ensure environment variables are loaded
let _storage: TelegramStorage | null = null;

export const storage = {
  get instance(): TelegramStorage {
    if (!_storage) {
      _storage = new TelegramStorage();
    }
    return _storage;
  },

  // Proxy all link methods to the instance
  async createShortenedLink(link: InsertLink & { shortCode: string; userId?: number }) {
    return this.instance.createShortenedLink(link);
  },

  async getShortenedLink(shortCode: string) {
    return this.instance.getShortenedLink(shortCode);
  },

  async updateLinkClicks(shortCode: string, userId?: number) {
    return this.instance.updateLinkClicks(shortCode, userId);
  },

  async getRecentLinks() {
    return this.instance.getRecentLinks();
  },

  async getUserLinks(userId: number) {
    return this.instance.getUserLinks(userId);
  },

  // Proxy all user methods to the instance
  async createUser(userData: InsertUser) {
    return this.instance.createUser(userData);
  },

  async getUserByUsername(username: string) {
    return this.instance.getUserByUsername(username);
  },

  async getUserById(id: number) {
    return this.instance.getUserById(id);
  },

  async updateUserPaymentDetails(userId: number, paymentData: any) {
    return this.instance.updateUserPaymentDetails(userId, paymentData);
  },

  async updateUserEarnings(userId: number, amount: number) {
    return this.instance.updateUserEarnings(userId, amount);
  },

  // Proxy all withdrawal methods to the instance
  async createWithdrawalRequest(userId: number, amount: number, method: string) {
    return this.instance.createWithdrawalRequest(userId, amount, method);
  },

  async getUserWithdrawals(userId: number) {
    return this.instance.getUserWithdrawals(userId);
  },

  async getAllUsers() {
    return this.instance.getAllUsers();
  },

  async getAllLinks() {
    return this.instance.getAllLinks();
  },

  async updateWithdrawalStatus(withdrawalId: number, status: string, adminUsername: string) {
    return this.instance.updateWithdrawalStatus(withdrawalId, status, adminUsername);
  },

  async restoreUserBalance(userId: number, withdrawalId: number) {
    return this.instance.restoreUserBalance(userId, withdrawalId);
  },

  // Temporary testing method
  async setUserBalanceForTesting(userId: number, balance: number) {
    return this.instance.setUserBalanceForTesting(userId, balance);
  }
};
