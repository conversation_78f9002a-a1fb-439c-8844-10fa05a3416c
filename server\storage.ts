import { type ShortenedLink, type InsertLink } from "@shared/schema";
import TelegramBot from 'node-telegram-bot-api';

export interface IStorage {
  createShortenedLink(link: InsertLink & { shortCode: string }): Promise<ShortenedLink>;
  getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined>;
  updateLinkClicks(shortCode: string): Promise<void>;
  getRecentLinks(): Promise<ShortenedLink[]>;
}

export class TelegramStorage implements IStorage {
  private bot: TelegramBot;
  private channelId: string;
  private cache: Map<string, ShortenedLink> = new Map();
  private initialized = false;

  constructor() {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const channelId = process.env.TELEGRAM_CHANNEL_ID;

    if (!botToken || !channelId) {
      throw new Error('TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID must be set');
    }

    this.bot = new TelegramBot(botToken);
    this.channelId = channelId;
  }

  private async initializeCache(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load existing links from Telegram channel
      await this.loadLinksFromChannel();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize Telegram storage cache:', error);
      // Continue without cache - new links will still work
      this.initialized = true;
    }
  }

  private async loadLinksFromChannel(): Promise<void> {
    try {
      // Get recent messages from the channel
      // Note: This is a simplified approach. In production, you might want to use a more robust method
      console.log('Loading existing links from Telegram channel...');
      // For now, we'll start with an empty cache and build it as we go
    } catch (error) {
      console.error('Error loading links from channel:', error);
    }
  }

  async createShortenedLink(linkData: InsertLink & { shortCode: string }): Promise<ShortenedLink> {
    await this.initializeCache();

    const link: ShortenedLink = {
      id: Date.now(), // Use timestamp as ID
      originalUrl: linkData.originalUrl,
      shortCode: linkData.shortCode,
      clicks: 0,
      createdAt: new Date(),
    };

    try {
      // Save to Telegram channel
      const message = JSON.stringify({
        type: 'shortened_link',
        data: link
      });

      await this.bot.sendMessage(this.channelId, message);

      // Cache the link
      this.cache.set(linkData.shortCode, link);

      console.log(`Saved link to Telegram: ${linkData.shortCode} -> ${linkData.originalUrl}`);
      return link;
    } catch (error) {
      console.error('Error saving link to Telegram:', error);
      throw new Error('Failed to save shortened link');
    }
  }

  async getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined> {
    await this.initializeCache();

    // First check cache
    const cachedLink = this.cache.get(shortCode);
    if (cachedLink) {
      return cachedLink;
    }

    // If not in cache, it might be an old link from before cache initialization
    // For now, return undefined. In a production system, you might want to search through channel history
    return undefined;
  }

  async updateLinkClicks(shortCode: string): Promise<void> {
    await this.initializeCache();

    const link = this.cache.get(shortCode);
    if (link) {
      link.clicks += 1;

      try {
        // Send updated click count to Telegram
        const message = JSON.stringify({
          type: 'click_update',
          shortCode: shortCode,
          clicks: link.clicks,
          timestamp: new Date().toISOString()
        });

        await this.bot.sendMessage(this.channelId, message);
      } catch (error) {
        console.error('Error updating clicks in Telegram:', error);
        // Don't throw error - click tracking is not critical
      }
    }
  }

  async getRecentLinks(): Promise<ShortenedLink[]> {
    await this.initializeCache();

    // Return recent links from cache, sorted by creation date
    const links = Array.from(this.cache.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    return links;
  }
}

// Lazy initialization to ensure environment variables are loaded
let _storage: TelegramStorage | null = null;

export const storage = {
  get instance(): TelegramStorage {
    if (!_storage) {
      _storage = new TelegramStorage();
    }
    return _storage;
  },

  // Proxy all methods to the instance
  async createShortenedLink(link: InsertLink & { shortCode: string }) {
    return this.instance.createShortenedLink(link);
  },

  async getShortenedLink(shortCode: string) {
    return this.instance.getShortenedLink(shortCode);
  },

  async updateLinkClicks(shortCode: string) {
    return this.instance.updateLinkClicks(shortCode);
  },

  async getRecentLinks() {
    return this.instance.getRecentLinks();
  }
};
