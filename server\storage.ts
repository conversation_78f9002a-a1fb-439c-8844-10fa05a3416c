import { shortenedLinks, type ShortenedLink, type InsertLink } from "@shared/schema";
import { db } from "./db";
import { eq, desc, sql } from "drizzle-orm";

export interface IStorage {
  createShortenedLink(link: InsertLink & { shortCode: string }): Promise<ShortenedLink>;
  getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined>;
  updateLinkClicks(shortCode: string): Promise<void>;
  getRecentLinks(): Promise<ShortenedLink[]>;
}

export class DatabaseStorage implements IStorage {
  async createShortenedLink(linkData: InsertLink & { shortCode: string }): Promise<ShortenedLink> {
    const [link] = await db
      .insert(shortenedLinks)
      .values({
        originalUrl: linkData.originalUrl,
        shortCode: linkData.shortCode,
        clicks: 0,
      })
      .returning();
    return link;
  }

  async getShortenedLink(shortCode: string): Promise<ShortenedLink | undefined> {
    const [link] = await db
      .select()
      .from(shortenedLinks)
      .where(eq(shortenedLinks.shortCode, shortCode));
    return link || undefined;
  }

  async updateLinkClicks(shortCode: string): Promise<void> {
    await db
      .update(shortenedLinks)
      .set({ clicks: sql`${shortenedLinks.clicks} + 1` })
      .where(eq(shortenedLinks.shortCode, shortCode));
  }

  async getRecentLinks(): Promise<ShortenedLink[]> {
    return db
      .select()
      .from(shortenedLinks)
      .orderBy(desc(shortenedLinks.createdAt))
      .limit(10);
  }
}

export const storage = new DatabaseStorage();
