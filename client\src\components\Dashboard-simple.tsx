import React, { useState, useEffect } from 'react';
import { 
  <PERSON>S<PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>ointer, 
  ExternalLink, 
  Copy, 
  LogOut,
  CreditCard
} from 'lucide-react';

interface User {
  id: number;
  username: string;
  totalEarnings: number;
  availableBalance: number;
  totalClicks: number;
  totalLinks: number;
  fullName?: string;
  address?: string;
  phoneNumber?: string;
  telegramUsername?: string;
  trafficSource?: string;
  trafficSourceLinks?: string;
  paymentMethod?: string;
  paymentDetails?: string;
  isPaymentVerified?: boolean;
}

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
}

export default function Dashboard({ user: initialUser, token, onLogout }: DashboardProps) {
  console.log('Dashboard received user:', initialUser);
  
  if (!initialUser) {
    return <div className="text-white p-4">No user data received</div>;
  }

  const [user, setUser] = useState<User>(initialUser);
  const [message, setMessage] = useState('');
  const [withdrawLoading, setWithdrawLoading] = useState(false);

  const handleWithdraw = async () => {
    if (!user.fullName || !user.paymentMethod) {
      setMessage('Please complete your payment details first');
      return;
    }

    if (user.availableBalance < 4) {
      setMessage('Minimum withdrawal amount is $4. Current balance: $' + user.availableBalance.toFixed(3));
      return;
    }

    const withdrawAmount = prompt(`Enter withdrawal amount (Available: $${user.availableBalance.toFixed(3)}, Minimum: $4):`);
    
    if (!withdrawAmount) return;
    
    const amount = parseFloat(withdrawAmount);
    
    if (isNaN(amount) || amount < 4) {
      setMessage('Invalid amount. Minimum withdrawal is $4');
      return;
    }

    if (amount > user.availableBalance) {
      setMessage('Insufficient balance');
      return;
    }

    setWithdrawLoading(true);
    try {
      const response = await fetch('/api/user/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ amount })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Withdrawal request submitted successfully! Admin will process your request.');
        // Update user balance
        setUser(prev => ({
          ...prev,
          availableBalance: prev.availableBalance - amount
        }));
      } else {
        setMessage(data.message || 'Withdrawal failed');
      }
    } catch (error) {
      setMessage('Withdrawal failed. Please try again.');
    } finally {
      setWithdrawLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Welcome back, {user.username}! 👋
              </h1>
              <p className="text-gray-300">Track your earnings and manage your links</p>
            </div>
            <button
              onClick={onLogout}
              className="flex items-center px-4 py-2 bg-red-500/20 text-red-200 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </button>
          </div>
        </div>

        {/* Simple Stats */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <h2 className="text-white text-xl mb-4">Stats</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-gray-300 text-sm">Total Earnings</p>
              <p className="text-2xl font-bold text-green-400">${user.totalEarnings?.toFixed(3) || '0.000'}</p>
            </div>
            <div className="text-center">
              <p className="text-gray-300 text-sm">Available Balance</p>
              <p className="text-2xl font-bold text-blue-400">${user.availableBalance?.toFixed(3) || '0.000'}</p>
            </div>
            <div className="text-center">
              <p className="text-gray-300 text-sm">Total Links</p>
              <p className="text-2xl font-bold text-purple-400">{user.totalLinks || 0}</p>
            </div>
            <div className="text-center">
              <p className="text-gray-300 text-sm">Total Clicks</p>
              <p className="text-2xl font-bold text-pink-400">{user.totalClicks || 0}</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <h2 className="text-white text-xl mb-4">Actions</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => alert('Payment modal will be implemented')}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <CreditCard className="w-4 h-4 mr-2 inline" />
              Payment
            </button>
            <button
              onClick={handleWithdraw}
              disabled={user.availableBalance < 4 || !user.fullName || !user.paymentMethod || withdrawLoading}
              className={`px-4 py-2 rounded-lg transition-colors ${
                user.availableBalance >= 4 && user.fullName && user.paymentMethod && !withdrawLoading
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-500 text-gray-300 cursor-not-allowed'
              }`}
            >
              <DollarSign className="w-4 h-4 mr-2 inline" />
              {withdrawLoading ? 'Processing...' : 'Withdraw'}
              {user.availableBalance < 4 && <span className="ml-1 text-xs">($4 min)</span>}
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-2 inline" />
              Homepage
            </button>
          </div>
        </div>

        {/* Message Display */}
        {message && (
          <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-6">
            <p className="text-blue-200">{message}</p>
          </div>
        )}

        {/* Debug Info */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <h2 className="text-white text-xl mb-4">Debug Info</h2>
          <div className="text-gray-300 text-sm">
            <p>User ID: {user.id}</p>
            <p>Username: {user.username}</p>
            <p>Has Payment Details: {user.fullName ? 'Yes' : 'No'}</p>
            <p>Payment Method: {user.paymentMethod || 'Not set'}</p>
            <p>Balance: ${user.availableBalance?.toFixed(3)}</p>
          </div>
        </div>

      </div>
    </div>
  );
}
