import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ointer,
  ExternalLink,
  Co<PERSON>,
  LogOut,
  CreditCard,
  AlertCircle,
  CheckCircle,
  TrendingUp
} from 'lucide-react';
import { getApiUrl } from '../config';

interface User {
  id: number;
  username: string;
  totalEarnings: number;
  availableBalance: number;
  totalClicks: number;
  totalLinks: number;
  fullName?: string;
  address?: string;
  phoneNumber?: string;
  telegramUsername?: string;
  trafficSource?: string;
  trafficSourceLinks?: string;
  paymentMethod?: string;
  paymentDetails?: string;
  isPaymentVerified?: boolean;
}

interface LinkItem {
  id: number;
  originalUrl: string;
  shortCode: string;
  clicks: number;
  earnings: number;
  createdAt: string;
}

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
}

export default function Dashboard({ user: initialUser, token, onLogout }: DashboardProps) {
  console.log('Dashboard received user:', initialUser);

  if (!initialUser) {
    return <div className="text-white p-4">No user data received</div>;
  }

  const [user, setUser] = useState<User>(initialUser);
  const [message, setMessage] = useState('');
  const [withdrawLoading, setWithdrawLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [links, setLinks] = useState<LinkItem[]>([]);
  const [newUrl, setNewUrl] = useState('');
  const [loading, setLoading] = useState(false);

  const [paymentData, setPaymentData] = useState({
    fullName: user.fullName || '',
    address: user.address || '',
    phoneNumber: user.phoneNumber || '',
    telegramUsername: user.telegramUsername || '',
    trafficSource: user.trafficSource || '',
    trafficSourceLinks: user.trafficSourceLinks || '',
    paymentMethod: user.paymentMethod || '',
    paymentDetails: user.paymentDetails || ''
  });

  useEffect(() => {
    fetchUserData();
    fetchUserLinks();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await fetch(getApiUrl('/api/auth/profile'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  const fetchUserLinks = async () => {
    setLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/user/links'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setLinks(data.links);
      }
    } catch (error) {
      console.error('Error fetching links:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateLink = async () => {
    if (!newUrl.trim()) {
      setMessage('Please enter a URL');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/links/shorten'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ originalUrl: newUrl })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Link created successfully!');
        setNewUrl('');
        fetchUserLinks();
        fetchUserData(); // Refresh user stats
      } else {
        setMessage(data.message || 'Failed to create link');
      }
    } catch (error) {
      setMessage('Failed to create link. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentDetails = async () => {
    // Client-side validation first
    const errors = [];

    if (!paymentData.fullName || paymentData.fullName.length < 2) {
      errors.push('Full name must be at least 2 characters');
    }
    if (!paymentData.address || paymentData.address.length < 10) {
      errors.push('Address must be at least 10 characters');
    }
    if (!paymentData.phoneNumber || paymentData.phoneNumber.length < 10) {
      errors.push('Phone number must be at least 10 characters');
    }
    if (!paymentData.telegramUsername || paymentData.telegramUsername.length < 1) {
      errors.push('Telegram username is required');
    }
    if (!paymentData.trafficSource || paymentData.trafficSource.length < 5) {
      errors.push('Traffic source description must be at least 5 characters');
    }
    if (!paymentData.trafficSourceLinks || paymentData.trafficSourceLinks.length < 10) {
      errors.push('Traffic source links must be at least 10 characters');
    }
    if (!paymentData.paymentMethod) {
      errors.push('Please select a payment method');
    }
    if (!paymentData.paymentDetails || paymentData.paymentDetails.length < 5) {
      errors.push('Payment details must be at least 5 characters');
    }

    if (errors.length > 0) {
      setMessage('Validation errors:\n• ' + errors.join('\n• '));
      return;
    }

    setPaymentLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/user/payment-details'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(paymentData)
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Payment details saved successfully! Admin will verify your information.');
        setShowPaymentModal(false);

        // Update user state immediately to reflect changes
        setUser(prev => ({
          ...prev,
          fullName: paymentData.fullName,
          address: paymentData.address,
          phoneNumber: paymentData.phoneNumber,
          telegramUsername: paymentData.telegramUsername,
          trafficSource: paymentData.trafficSource,
          trafficSourceLinks: paymentData.trafficSourceLinks,
          paymentMethod: paymentData.paymentMethod,
          paymentDetails: paymentData.paymentDetails,
          isPaymentVerified: true // Auto-verified, no admin verification needed
        }));

        fetchUserData(); // Refresh user data from server
      } else {
        if (data.errors) {
          // Show detailed validation errors from server
          const errorMessages = data.errors.map((err: any) => err.message).join('\n• ');
          setMessage('Validation failed:\n• ' + errorMessages);
        } else {
          setMessage(data.message || 'Failed to save payment details');
        }
      }
    } catch (error) {
      setMessage('Failed to save payment details. Please try again.');
    } finally {
      setPaymentLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setMessage('Link copied to clipboard!');
    setTimeout(() => setMessage(''), 3000);
  };

  const handleWithdraw = async () => {
    // Show warning if balance is below minimum but still allow withdrawal request
    let warningMessage = '';
    if (user.availableBalance < 4) {
      warningMessage = `⚠️ WARNING: Your current balance ($${user.availableBalance.toFixed(3)}) is below the minimum payout of $4.00.\n\nYou can still submit a withdrawal request, but payment will only be processed after you reach the minimum balance of $4.00.\n\n`;
    }

    const withdrawAmount = prompt(
      `${warningMessage}Enter withdrawal amount (Available: $${user.availableBalance.toFixed(3)}, Minimum payout: $4.00):\n\nNote: You can request any amount, but payment is only processed when you have at least $4.00 total balance.`
    );

    if (!withdrawAmount) return;

    const amount = parseFloat(withdrawAmount);

    if (isNaN(amount) || amount <= 0) {
      setMessage('Invalid amount. Please enter a valid positive number');
      return;
    }

    if (amount > user.availableBalance) {
      setMessage('Cannot request more than your available balance');
      return;
    }

    setWithdrawLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/user/withdraw'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ amount })
      });

      const data = await response.json();

      if (response.ok) {
        if (user.availableBalance >= 4) {
          setMessage('✅ Withdrawal request submitted successfully! Your request meets the minimum balance requirement and will be processed by admin.');
          // Update user balance only if it meets minimum requirement
          setUser(prev => ({
            ...prev,
            availableBalance: prev.availableBalance - amount
          }));
        } else {
          setMessage(`✅ Withdrawal request submitted! Note: Payment will be processed only after you reach the minimum balance of $4.00. Current balance: $${user.availableBalance.toFixed(3)}`);
          // Don't deduct balance if below minimum - keep original balance
        }
        // Always refresh user data from server to get accurate balance
        fetchUserData();
      } else {
        setMessage(data.message || 'Withdrawal failed');
      }
    } catch (error) {
      setMessage('Withdrawal failed. Please try again.');
    } finally {
      setWithdrawLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Welcome back, {user.username}! 👋
              </h1>
              <p className="text-gray-300">Track your earnings and manage your links</p>
            </div>
            <button
              onClick={onLogout}
              className="flex items-center px-4 py-2 bg-red-500/20 text-red-200 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </button>
          </div>
        </div>

        {/* Payment Status */}
        {user.fullName ? (
          <div className="bg-green-500/20 border-green-500/50 rounded-2xl p-6 mb-6 border">
            <div className="flex items-start">
              <CheckCircle className="w-6 h-6 text-green-400 mr-3 mt-1" />
              <div className="flex-1">
                <h3 className="font-medium mb-2 text-green-200">
                  Payment Details Saved ✅
                </h3>
                <p className="text-green-300">
                  Your payment details have been saved successfully. You can withdraw your earnings when you reach the minimum balance of $4.00!
                </p>
                <button
                  onClick={() => setShowPaymentModal(true)}
                  className="mt-3 inline-flex items-center px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                >
                  Update Details
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-blue-500/20 border-blue-500/50 rounded-2xl p-6 mb-6 border">
            <div className="flex items-start">
              <AlertCircle className="w-6 h-6 text-blue-400 mr-3 mt-1" />
              <div className="flex-1">
                <h3 className="font-medium mb-2 text-blue-200">
                  Payment Details Optional 💡
                </h3>
                <p className="text-blue-300">
                  You can withdraw your earnings even without payment details. However, adding payment details helps admin process your withdrawal faster!
                </p>
                <button
                  onClick={() => setShowPaymentModal(true)}
                  className="mt-3 inline-flex items-center px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                >
                  Add Payment Details
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Earnings</p>
                <p className="text-2xl font-bold text-green-400">
                  ${user.totalEarnings?.toFixed(3) || '0.000'}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Available Balance</p>
                <p className="text-2xl font-bold text-blue-400">
                  ${user.availableBalance?.toFixed(3) || '0.000'}
                </p>
                {user.availableBalance < 4 && (
                  <p className="text-yellow-400 text-xs mt-1">
                    💡 Minimum payout: $4.00
                  </p>
                )}
              </div>
              <CreditCard className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Links</p>
                <p className="text-2xl font-bold text-purple-400">{user.totalLinks || 0}</p>
              </div>
              <Link className="w-8 h-8 text-purple-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Clicks</p>
                <p className="text-2xl font-bold text-pink-400">{user.totalClicks || 0}</p>
              </div>
              <MousePointer className="w-8 h-8 text-pink-400" />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mb-6">
          {/* Payment Button */}
          <button
            onClick={() => setShowPaymentModal(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            <CreditCard className="w-4 h-4 mr-2" />
            Payment
          </button>

          {/* Withdraw Button - Always Enabled */}
          <button
            onClick={handleWithdraw}
            disabled={withdrawLoading}
            className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              !withdrawLoading
                ? 'bg-green-500 text-white hover:bg-green-600'
                : 'bg-gray-500 text-gray-300 cursor-not-allowed'
            }`}
          >
            {withdrawLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <DollarSign className="w-4 h-4 mr-2" />
                Withdraw
                {user.availableBalance < 4 && (
                  <span className="ml-1 text-xs">($4 min)</span>
                )}
              </>
            )}
          </button>

          {/* Homepage Button */}
          <button
            onClick={() => window.location.href = '/'}
            className="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors font-medium"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Homepage
          </button>
        </div>

        {/* Message Display */}
        {message && (
          <div className={`rounded-lg p-3 mb-6 ${
            message.includes('successfully') || message.includes('Success')
              ? 'bg-green-500/20 border border-green-500/50 text-green-200'
              : 'bg-red-500/20 border border-red-500/50 text-red-200'
          }`}>
            <pre className="whitespace-pre-wrap font-sans">{message}</pre>
          </div>
        )}

        {/* Create Link Section */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4">Create New Link</h2>
          <div className="flex gap-4">
            <input
              type="url"
              value={newUrl}
              onChange={(e) => setNewUrl(e.target.value)}
              className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
              placeholder="Enter URL to shorten (e.g., https://example.com)"
            />
            <button
              onClick={handleCreateLink}
              disabled={loading || !newUrl.trim()}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {loading ? 'Creating...' : 'Create Link'}
            </button>
          </div>
          <p className="text-gray-400 text-sm mt-2">
            💰 Earn $0.007 for every click on your shortened links!
          </p>
        </div>

        {/* Recent Links */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4">Your Links</h2>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
              <p className="text-gray-300 mt-2">Loading your links...</p>
            </div>
          ) : links.length === 0 ? (
            <div className="text-center py-8">
              <Link className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-300">No links created yet</p>
              <p className="text-gray-400 text-sm">Create your first link to start earning!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {links.map((link) => (
                <div key={link.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <p className="text-white font-medium truncate mr-4">
                          {link.originalUrl}
                        </p>
                        <span className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded">
                          {link.shortCode}
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-300 space-x-4">
                        <span className="flex items-center">
                          <MousePointer className="w-4 h-4 mr-1" />
                          {link.clicks} clicks
                        </span>
                        <span className="flex items-center">
                          <DollarSign className="w-4 h-4 mr-1" />
                          ${(link.earnings || 0).toFixed(3)}
                        </span>
                        <span>{new Date(link.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => copyToClipboard(`${window.location.origin}/s/${link.shortCode}`)}
                        className="p-2 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-colors"
                        title="Copy link"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <a
                        href={`/s/${link.shortCode}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-purple-500/20 text-purple-300 rounded-lg hover:bg-purple-500/30 transition-colors"
                        title="Open link"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

      </div>

      {/* Payment Details Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 w-full max-w-2xl border border-white/20 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-white mb-6">Payment Details & Verification</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name * (min 2 characters)
                  </label>
                  <input
                    type="text"
                    value={paymentData.fullName}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, fullName: e.target.value }))}
                    className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                      paymentData.fullName.length >= 2 ? 'border-green-500/50' : 'border-white/20'
                    }`}
                    placeholder="Enter your full name (e.g., John Smith)"
                    required
                  />
                  <p className="text-xs text-gray-400 mt-1">{paymentData.fullName.length}/2+ characters</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={paymentData.phoneNumber}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="+1234567890"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Complete Address * (min 10 characters)
                </label>
                <textarea
                  value={paymentData.address}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, address: e.target.value }))}
                  className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    paymentData.address.length >= 10 ? 'border-green-500/50' : 'border-white/20'
                  }`}
                  placeholder="123 Main Street, New York, NY 10001, USA"
                  rows={3}
                  required
                />
                <p className="text-xs text-gray-400 mt-1">{paymentData.address.length}/10+ characters</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Telegram Username *
                  </label>
                  <input
                    type="text"
                    value={paymentData.telegramUsername}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, telegramUsername: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="@yourusername"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Payment Method *
                  </label>
                  <select
                    value={paymentData.paymentMethod}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-400 [&>option]:bg-gray-800 [&>option]:text-white"
                    required
                  >
                    <option value="" className="bg-gray-800 text-white">Select payment method</option>
                    <option value="upi" className="bg-gray-800 text-white">🇮🇳 UPI ID (India)</option>
                    <option value="paypal" className="bg-gray-800 text-white">💳 PayPal</option>
                    <option value="bank" className="bg-gray-800 text-white">🏦 Bank Transfer</option>
                    <option value="skrill" className="bg-gray-800 text-white">💰 Skrill</option>
                    <option value="other" className="bg-gray-800 text-white">🔧 Other (Contact Support)</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Payment Details *
                </label>
                {paymentData.paymentMethod === 'other' ? (
                  <div className="space-y-3">
                    <textarea
                      value={paymentData.paymentDetails}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, paymentDetails: e.target.value }))}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                      placeholder="Describe your preferred payment method (e.g., Crypto wallet, Mobile money, etc.)"
                      rows={3}
                      required
                    />
                    <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-3">
                      <p className="text-blue-200 text-sm">
                        📞 <strong>Contact Support:</strong> Since you selected "Other", please contact our support team at{' '}
                        <span className="font-mono bg-blue-500/30 px-1 rounded">@buckyop</span> on Telegram to discuss your payment method.
                      </p>
                    </div>
                  </div>
                ) : (
                  <input
                    type="text"
                    value={paymentData.paymentDetails}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, paymentDetails: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder={
                      paymentData.paymentMethod === 'upi' ? 'yourname@paytm (e.g., john@paytm, mary@phonepe)' :
                      paymentData.paymentMethod === 'paypal' ? '<EMAIL>' :
                      paymentData.paymentMethod === 'bank' ? 'Account Number, Bank Name, IFSC Code' :
                      paymentData.paymentMethod === 'skrill' ? '<EMAIL>' :
                      'Enter payment details'
                    }
                    required
                  />
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Traffic Source Description * (min 5 characters)
                </label>
                <textarea
                  value={paymentData.trafficSource}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, trafficSource: e.target.value }))}
                  className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    paymentData.trafficSource.length >= 5 ? 'border-green-500/50' : 'border-white/20'
                  }`}
                  placeholder="Describe where you get traffic for your links (social media, websites, etc.)"
                  rows={2}
                  required
                />
                <p className="text-xs text-gray-400 mt-1">{paymentData.trafficSource.length}/5+ characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Traffic Source Links * (min 10 characters)
                </label>
                <textarea
                  value={paymentData.trafficSourceLinks}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, trafficSourceLinks: e.target.value }))}
                  className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    paymentData.trafficSourceLinks.length >= 10 ? 'border-green-500/50' : 'border-white/20'
                  }`}
                  placeholder="Provide links to your channels/websites where you share links:&#10;• YouTube: https://youtube.com/@yourchannel&#10;• Instagram: https://instagram.com/yourpage&#10;• Website: https://yourwebsite.com&#10;• Telegram: https://t.me/yourchannel"
                  rows={4}
                  required
                />
                <p className="text-xs text-gray-400 mt-1">
                  📝 {paymentData.trafficSourceLinks.length}/10+ characters - Provide actual links to verify your traffic sources
                </p>
              </div>

              <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4">
                <h4 className="text-blue-200 font-medium mb-2">📋 Verification Process</h4>
                <ul className="text-blue-300 text-sm space-y-1">
                  <li>• Admin will verify your information manually</li>
                  <li>• Ensure all details are accurate and complete</li>
                  <li>• Verification may take 24-48 hours</li>
                  <li>• You can withdraw once verified</li>
                  <li>• For "Other" payment methods, contact support first</li>
                </ul>
                <div className="mt-3 pt-3 border-t border-blue-500/30">
                  <p className="text-blue-200 text-sm">
                    📞 <strong>Need Help?</strong> Contact support at{' '}
                    <span className="font-mono bg-blue-500/30 px-1 rounded">@buckyop</span> on Telegram
                  </p>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="flex-1 px-4 py-3 bg-gray-500/20 text-gray-300 rounded-lg hover:bg-gray-500/30 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePaymentDetails}
                  disabled={paymentLoading || !paymentData.fullName || !paymentData.paymentMethod}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {paymentLoading ? 'Saving...' : 'Save Payment Details'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
