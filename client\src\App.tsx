import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useState, useEffect } from "react";
import Home from "@/pages/home";
import RedirectStep1 from "@/pages/redirect-step1";
import RedirectStep2 from "@/pages/redirect-step2";
import RedirectFinal from "@/pages/redirect-final";
import NotFound from "@/pages/not-found";
import Auth from "@/components/Auth";
import Dashboard from "@/components/Dashboard";

interface User {
  id: number;
  username: string;
  totalEarnings: number;
  availableBalance: number;
  totalClicks: number;
  totalLinks: number;
  telegramId?: string;
  mobileNumber?: string;
  withdrawalMethod?: string;
}

function Router({ user, token, onLogin, onLogout }: {
  user: User | null;
  token: string | null;
  onLogin: (user: User, token: string) => void;
  onLogout: () => void;
}) {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/auth" component={() => <Auth onLogin={onLogin} />} />
      <Route path="/dashboard" component={() =>
        user && token ? (
          <Dashboard user={user} token={token} onLogout={onLogout} />
        ) : (
          <Auth onLogin={onLogin} />
        )
      } />
      <Route path="/redirect-step1" component={RedirectStep1} />
      <Route path="/redirect-step2" component={RedirectStep2} />
      <Route path="/redirect-final" component={RedirectFinal} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    // Check for stored authentication
    const storedToken = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (storedToken && storedUser) {
      try {
        setToken(storedToken);
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }
  }, []);

  const handleLogin = (userData: User, userToken: string) => {
    setUser(userData);
    setToken(userToken);
  };

  const handleLogout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router
          user={user}
          token={token}
          onLogin={handleLogin}
          onLogout={handleLogout}
        />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
