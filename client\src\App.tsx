import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Home from "@/pages/home";
import RedirectStep1 from "@/pages/redirect-step1";
import RedirectStep2 from "@/pages/redirect-step2";
import RedirectFinal from "@/pages/redirect-final";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/redirect-step1" component={RedirectStep1} />
      <Route path="/redirect-step2" component={RedirectStep2} />
      <Route path="/redirect-final" component={RedirectFinal} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
