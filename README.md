# GameHub - Gaming News & Link Shortener

A comprehensive gaming hub website featuring news, deals, and link shortening with a multi-step redirect system and ad integration.

## Features

- **Gaming News Section**: Dynamic news content loaded from environment variables
- **Gaming Deals Section**: Product deals with pricing and discount information
- **Link Shortener**: URL shortening service with click analytics
- **Multi-Step Redirect System**: Complex redirect flow with countdown timers and ad integration
- **Ad Integration**: Monetag MultiTag and native banner (interstitial) ads
- **PostgreSQL Database**: Persistent storage for shortened links
- **Responsive Design**: Gaming-themed UI with glassmorphism effects

## Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Node.js + Express.js
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: TanStack Query
- **Routing**: Wouter

## Deployment on Render

### Method 1: Automatic Deployment (Using render.yaml)

1. Push this project to your GitHub repository
2. Go to [Render Dashboard](https://dashboard.render.com)
3. Click "New" → "Blueprint"
4. Connect your GitHub repository
5. Render will automatically create both the database and web service
6. Update the `BASE_URL` environment variable with your actual Render URL

### Method 2: Manual Setup

#### 1. Database Setup

1. Create a PostgreSQL database on Render:
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New" → "PostgreSQL"
   - Name your database (e.g., "gamehub-db")
   - Choose your plan and region
   - Click "Create Database"
   - Copy the "External Database URL" for later use

#### 2. Web Service Setup

1. Create a new Web Service on Render:
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - Configure the service:
     - **Name**: Your app name (e.g., "gamehub-app")
     - **Environment**: Node
     - **Build Command**: `npm ci && npm run build`
     - **Start Command**: `npm start`

#### 3. Environment Variables

Set the following environment variables in your Render web service:

#### Required Variables:
```
DATABASE_URL=your_postgresql_external_database_url_here
NODE_ENV=production
BASE_URL=https://your-app-name.onrender.com
```

#### News Configuration (Optional - Add as many as needed):
```
NEWS_1={"title":"Your News Title","excerpt":"News excerpt text","category":"Gaming","date":"2024-01-15","readTime":"3 min read","imageUrl":"https://your-image-url.com/image.jpg"}

NEWS_2={"title":"Another News Title","excerpt":"Another news excerpt","category":"eSports","date":"2024-01-14","readTime":"5 min read","imageUrl":"https://your-image-url.com/image2.jpg"}

NEWS_3={"title":"Third News Title","excerpt":"Third news excerpt","category":"Hardware","date":"2024-01-13","readTime":"4 min read","imageUrl":"https://your-image-url.com/image3.jpg"}
```

#### Deals Configuration (Optional - Add as many as needed):
```
DEAL_1={"title":"Gaming Headset","currentPrice":"$159.99","originalPrice":"$249.99","discount":"36% OFF","timeLeft":"5 hours left","imageUrl":"https://your-image-url.com/headset.jpg"}

DEAL_2={"title":"Gaming Keyboard","currentPrice":"$129.99","originalPrice":"$179.99","discount":"28% OFF","timeLeft":"1 day left","imageUrl":"https://your-image-url.com/keyboard.jpg"}

DEAL_3={"title":"Gaming Mouse","currentPrice":"$79.99","originalPrice":"$119.99","discount":"33% OFF","timeLeft":"3 days left","imageUrl":"https://your-image-url.com/mouse.jpg"}
```

#### 4. Set up Telegram Bot (Required for Link Shortener)

1. **Create a Telegram Bot:**
   - Open Telegram and search for `@BotFather`
   - Send `/newbot` and follow instructions
   - Save the bot token (e.g., `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

2. **Create a Private Channel:**
   - Create a new private channel in Telegram
   - Add your bot as an administrator with posting permissions
   - Get the channel ID (use `@userinfobot` or check channel URL)
   - Channel ID will be negative (e.g., `-1001234567890`)

3. **Add Environment Variables:**
   ```
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   TELEGRAM_CHANNEL_ID=your_channel_id_here
   ```

#### 5. Deploy

1. Click "Create Web Service"
2. Render will automatically build and deploy your application
3. Once deployed, test the storage by visiting: `https://your-app-url.onrender.com/api/storage/test`

**Important Notes:**
- Replace `https://gamehub-app.onrender.com` in the `BASE_URL` environment variable with your actual Render app URL
- Add your actual Telegram bot token and channel ID to the environment variables
- If build fails with "vite not found", manually add `vite` and `esbuild` to your dependencies: `npm install vite esbuild --save`
- After successful deployment, run `https://your-app-url.onrender.com` to ensure the app is working

## Troubleshooting

### Link Shortener Shows "Invalid request data" Error

If the link shortener feature shows a 400 error in production:

1. **Check Render Logs**: Go to your Render dashboard → your web service → Logs
2. **Verify Environment Variables**: Ensure `BASE_URL`, `TELEGRAM_BOT_TOKEN`, and `TELEGRAM_CHANNEL_ID` are set correctly
3. **Test Telegram Storage**: Visit `https://your-app-url.onrender.com/api/storage/test` to check Telegram bot connectivity
4. **Verify Bot Permissions**: Ensure your bot is added as admin to the private channel with posting permissions
5. **Test API Manually**: Use curl to test the API endpoint:
   ```bash
   curl -X POST https://your-app-url.onrender.com/api/links/shorten \
        -H "Content-Type: application/json" \
        -d '{"originalUrl":"https://example.com"}'
   ```

### Common Issues:

- **Build Fails**: Ensure all build dependencies are in `dependencies` not `devDependencies`
- **Telegram Storage Errors**:
  - Check if `TELEGRAM_BOT_TOKEN` is valid and bot is active
  - Verify `TELEGRAM_CHANNEL_ID` is correct (should be negative for channels)
  - Ensure bot has admin permissions in the channel
  - Test storage using `/api/storage/test` endpoint
- **"Invalid request data" Error**: Usually indicates Telegram bot configuration issues, not form validation
- **CORS Issues**: This shouldn't happen as frontend and backend are served from same domain
- **502/503 Errors**: Usually indicates server startup issues, check Render logs for details

### Database Setup Issues:

If using Render's PostgreSQL:
1. Ensure both services are in the same region (e.g., Oregon)
2. Use the **Internal Database URL** for `DATABASE_URL` (starts with `postgresql://`)
3. Make sure the database service is running before the web service starts
4. The DATABASE_URL format should be: `********************************************`

**Example DATABASE_URL:**
```
***********************************************************************************************
```

## Environment Variables Reference

### Required:
- `DATABASE_URL`: PostgreSQL connection string from Render database
- `NODE_ENV`: Set to "production" for deployment
- `BASE_URL`: Your site's base URL (e.g., https://your-app-name.onrender.com) - This ensures shortened links use your production domain

### Content Management:
- `NEWS_X`: JSON objects containing news article data (X = 1, 2, 3, etc.)
- `DEAL_X`: JSON objects containing deal information (X = 1, 2, 3, etc.)

### News Object Format:
```json
{
  "title": "Article Title",
  "excerpt": "Article summary/excerpt",
  "category": "Gaming|eSports|Hardware|Reviews",
  "date": "2024-01-15",
  "readTime": "3 min read",
  "imageUrl": "https://image-url.com/image.jpg"
}
```

### Deal Object Format:
```json
{
  "title": "Product Name",
  "currentPrice": "$99.99",
  "originalPrice": "$149.99",
  "discount": "33% OFF",
  "timeLeft": "2 days left",
  "imageUrl": "https://image-url.com/product.jpg"
}
```

## Multi-Step Redirect System

The application includes a complex redirect system with three steps:

1. **Step 1**: Gaming news blog (20s countdown + 10s additional countdown)
2. **Step 2**: Gaming career blog (10s countdown + 20s additional countdown)  
3. **Final**: Secure gateway (20s countdown + 5s final countdown)

Each step includes scroll requirements and ad integration before users can proceed to the next step.

## Ad Integration

The application includes two ad systems:

1. **Monetag MultiTag**: Automatic ad format optimization (zone: 156349)
2. **Native Banner (Interstitial)**: Interstitial ads from groleegni.net (zone: 9550747)

Both ad systems are active on all redirect pages for maximum monetization.

## API Endpoints

- `GET /api/news` - Get all news articles
- `GET /api/deals` - Get all deals
- `POST /api/links/shorten` - Shorten a URL
- `GET /api/links/recent` - Get recent shortened links
- `GET /api/links/original/:shortCode` - Get original URL by short code

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Push database schema
npm run db:push
```

## Support

For deployment issues or questions, refer to [Render Documentation](https://render.com/docs) or create an issue in this repository.