import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { storage } from './storage';
import { insertUserSchema, loginSchema } from '@shared/schema';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

export interface AuthRequest extends Request {
  user?: {
    id: number;
    username: string;
  };
}

// Generate JWT token
export function generateToken(user: { id: number; username: string }): string {
  return jwt.sign(
    { id: user.id, username: user.username },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
}

// Verify JWT token middleware
export function authenticateToken(req: AuthRequest, res: Response, next: NextFunction) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' });
    }
    
    req.user = decoded as { id: number; username: string };
    next();
  });
}

// Optional authentication - doesn't fail if no token
export function optionalAuth(req: AuthRequest, res: Response, next: NextFunction) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (!err) {
        req.user = decoded as { id: number; username: string };
      }
    });
  }
  
  next();
}

// Register new user
export async function register(req: Request, res: Response) {
  try {
    const validation = insertUserSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validation.error.errors
      });
    }

    const { username, password } = validation.data;

    // Check if user already exists
    const existingUser = await storage.getUserByUsername(username);
    if (existingUser) {
      return res.status(409).json({ message: 'Username already exists' });
    }

    // Create user
    const user = await storage.createUser({ username, password });

    // Generate token
    const token = generateToken({ id: user.id, username: user.username });

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        createdAt: user.createdAt
      },
      token
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Failed to create user' });
  }
}

// Login user
export async function login(req: Request, res: Response) {
  try {
    const validation = loginSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validation.error.errors
      });
    }

    const { username, password } = validation.data;

    // Get user
    const user = await storage.getUserByUsername(username);
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate token
    const token = generateToken({ id: user.id, username: user.username });

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        fullName: user.fullName,
        address: user.address,
        phoneNumber: user.phoneNumber,
        telegramUsername: user.telegramUsername,
        trafficSource: user.trafficSource,
        paymentMethod: user.paymentMethod,
        paymentDetails: user.paymentDetails,
        isPaymentVerified: user.isPaymentVerified,
        createdAt: user.createdAt
      },
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Login failed' });
  }
}

// Get current user profile
export async function getProfile(req: AuthRequest, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const user = await storage.getUserById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        fullName: user.fullName,
        address: user.address,
        phoneNumber: user.phoneNumber,
        telegramUsername: user.telegramUsername,
        trafficSource: user.trafficSource,
        paymentMethod: user.paymentMethod,
        paymentDetails: user.paymentDetails,
        isPaymentVerified: user.isPaymentVerified,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ message: 'Failed to get profile' });
  }
}
