services:
  # Web Service (No database needed - using Telegram storage)
  - type: web
    name: gamehub-app
    env: node
    plan: free
    region: oregon
    buildCommand: npm ci && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: BASE_URL
        value: https://gamehub-app.onrender.com
      - key: TELEGRAM_BOT_TOKEN
        value: **********************************************
      - key: TELEGRAM_CHANNEL_ID
        value: -1002166499041