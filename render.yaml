services:
  # Web Service (No database needed - using Telegram storage)
  - type: web
    name: gamehub-app
    env: node
    plan: free
    region: oregon
    buildCommand: npm ci && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: BASE_URL
        value: https://gamehub-app.onrender.com
      - key: TELEGRAM_BOT_TOKEN
        value: YOUR_BOT_TOKEN_HERE
      - key: TELEGRAM_CHANNEL_ID
        value: YOUR_CHANNEL_ID_HERE