services:
  # PostgreSQL Database
  - type: pserv
    name: gamehub-db
    env: postgres
    plan: free
    region: oregon

  # Web Service  
  - type: web
    name: gamehub-app
    env: node
    plan: free
    region: oregon
    buildCommand: npm ci && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: BASE_URL
        value: https://gamehub-app.onrender.com
      - key: DATABASE_URL
        fromDatabase:
          name: gamehub-db
          property: connectionString