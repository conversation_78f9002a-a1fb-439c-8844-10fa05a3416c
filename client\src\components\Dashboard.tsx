import React, { useState, useEffect } from 'react';
import { 
  DollarS<PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>ointer, 
  TrendingUp, 
  ExternalLink, 
  Copy, 
  LogOut,
  Smartphone,
  CreditCard,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface User {
  id: number;
  username: string;
  totalEarnings: number;
  availableBalance: number;
  totalClicks: number;
  totalLinks: number;
  // Payment details
  fullName?: string;
  address?: string;
  phoneNumber?: string;
  telegramUsername?: string;
  trafficSource?: string;
  trafficSourceLinks?: string;
  paymentMethod?: string;
  paymentDetails?: string;
  isPaymentVerified?: boolean;
}

interface Link {
  id: number;
  originalUrl: string;
  shortCode: string;
  clicks: number;
  earnings: number;
  createdAt: string;
}

interface DashboardProps {
  user: User;
  token: string;
  onLogout: () => void;
}

export default function Dashboard({ user: initialUser, token, onLogout }: DashboardProps) {
  const [user, setUser] = useState<User>(initialUser);
  const [links, setLinks] = useState<Link[]>([]);
  const [loading, setLoading] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [withdrawLoading, setWithdrawLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [paymentData, setPaymentData] = useState({
    fullName: user.fullName || '',
    address: user.address || '',
    phoneNumber: user.phoneNumber || '',
    telegramUsername: user.telegramUsername || '',
    trafficSource: user.trafficSource || '',
    trafficSourceLinks: user.trafficSourceLinks || '',
    paymentMethod: user.paymentMethod || '',
    paymentDetails: user.paymentDetails || ''
  });

  useEffect(() => {
    fetchUserData();
    fetchUserLinks();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  const fetchUserLinks = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/links', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setLinks(data.links);
      }
    } catch (error) {
      console.error('Error fetching links:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentDetails = async () => {
    setPaymentLoading(true);
    try {
      const response = await fetch('/api/user/payment-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(paymentData)
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Payment details saved successfully! Admin will verify your information.');
        setShowPaymentModal(false);

        // Update user state immediately to reflect changes
        setUser(prev => ({
          ...prev,
          fullName: paymentData.fullName,
          address: paymentData.address,
          phoneNumber: paymentData.phoneNumber,
          telegramUsername: paymentData.telegramUsername,
          trafficSource: paymentData.trafficSource,
          trafficSourceLinks: paymentData.trafficSourceLinks,
          paymentMethod: paymentData.paymentMethod,
          paymentDetails: paymentData.paymentDetails,
          isPaymentVerified: false // Will be verified by admin
        }));

        fetchUserData(); // Refresh user data from server
      } else {
        setMessage(data.message || 'Failed to save payment details');
      }
    } catch (error) {
      setMessage('Failed to save payment details. Please try again.');
    } finally {
      setPaymentLoading(false);
    }
  };

  const handleWithdraw = async () => {
    const amount = parseFloat(withdrawAmount);
    if (amount < 4) {
      setMessage('Minimum withdrawal amount is $4.00');
      return;
    }

    if (amount > user.availableBalance) {
      setMessage('Insufficient balance');
      return;
    }

    if (!user.fullName || !user.paymentMethod) {
      setMessage('Please complete your payment details first');
      setShowPaymentModal(true);
      return;
    }

    setWithdrawLoading(true);
    try {
      const response = await fetch('/api/user/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          amount: amount
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Withdrawal request submitted successfully! Admin will process your request.');
        setShowWithdrawModal(false);
        setWithdrawAmount('');
        fetchUserData(); // Refresh user data
      } else {
        setMessage(data.message || 'Withdrawal failed');
      }
    } catch (error) {
      setMessage('Withdrawal failed. Please try again.');
    } finally {
      setWithdrawLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setMessage('Link copied to clipboard!');
    setTimeout(() => setMessage(''), 3000);
  };

  const needsPaymentSetup = !user.fullName || !user.paymentMethod;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Welcome back, {user.username}! 👋
              </h1>
              <p className="text-gray-300">Track your earnings and manage your links</p>
            </div>
            <button
              onClick={onLogout}
              className="flex items-center px-4 py-2 bg-red-500/20 text-red-200 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </button>
          </div>
        </div>

        {/* Payment Setup Alert */}
        {needsPaymentSetup && (
          <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-2xl p-6 mb-6">
            <div className="flex items-start">
              <AlertCircle className="w-6 h-6 text-yellow-400 mr-3 mt-1" />
              <div className="flex-1">
                <h3 className="text-yellow-200 font-medium mb-2">Complete Payment Setup</h3>
                <p className="text-yellow-300 mb-4">
                  To withdraw your earnings, please complete your payment details and verification information.
                </p>
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={() => setShowPaymentModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-yellow-500 text-yellow-900 rounded-lg hover:bg-yellow-400 transition-colors font-medium"
                  >
                    <CreditCard className="w-4 h-4 mr-2" />
                    Add Payment Details
                  </button>
                  <button
                    onClick={() => window.location.href = '/'}
                    className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Go to Homepage
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Verification Status */}
        {user.fullName && (
          <div className={`rounded-2xl p-6 mb-6 border ${
            user.isPaymentVerified
              ? 'bg-green-500/20 border-green-500/50'
              : 'bg-blue-500/20 border-blue-500/50'
          }`}>
            <div className="flex items-start">
              {user.isPaymentVerified ? (
                <CheckCircle className="w-6 h-6 text-green-400 mr-3 mt-1" />
              ) : (
                <AlertCircle className="w-6 h-6 text-blue-400 mr-3 mt-1" />
              )}
              <div className="flex-1">
                <h3 className={`font-medium mb-2 ${
                  user.isPaymentVerified ? 'text-green-200' : 'text-blue-200'
                }`}>
                  {user.isPaymentVerified ? 'Payment Details Verified ✅' : 'Payment Details Under Review'}
                </h3>
                <p className={user.isPaymentVerified ? 'text-green-300' : 'text-blue-300'}>
                  {user.isPaymentVerified
                    ? 'Your payment details have been verified. You can now withdraw your earnings!'
                    : 'Admin is reviewing your payment details. You can withdraw once verified.'
                  }
                </p>
                {!user.isPaymentVerified && (
                  <button
                    onClick={() => setShowPaymentModal(true)}
                    className="mt-3 inline-flex items-center px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                  >
                    Update Details
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Earnings</p>
                <p className="text-2xl font-bold text-green-400">
                  ${user.totalEarnings.toFixed(3)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Available Balance</p>
                <p className="text-2xl font-bold text-blue-400">
                  ${user.availableBalance.toFixed(3)}
                </p>
              </div>
              <CreditCard className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Links</p>
                <p className="text-2xl font-bold text-purple-400">{user.totalLinks}</p>
              </div>
              <Link className="w-8 h-8 text-purple-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Clicks</p>
                <p className="text-2xl font-bold text-pink-400">{user.totalClicks}</p>
              </div>
              <MousePointer className="w-8 h-8 text-pink-400" />
            </div>
          </div>
        </div>

        {/* Withdraw Button */}
        {user.fullName && user.paymentMethod && user.availableBalance >= 4 && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium mb-1">Ready to Withdraw!</h3>
                <p className="text-gray-300 text-sm">
                  You have ${user.availableBalance.toFixed(3)} available for withdrawal
                </p>
              </div>
              <button
                onClick={() => setShowWithdrawModal(true)}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium"
              >
                Withdraw Money
              </button>
            </div>
          </div>
        )}

        {/* Message */}
        {message && (
          <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3 mb-6 text-green-200">
            {message}
          </div>
        )}

        {/* Recent Links */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4">Your Links</h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
              <p className="text-gray-300 mt-2">Loading your links...</p>
            </div>
          ) : links.length === 0 ? (
            <div className="text-center py-8">
              <Link className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-300">No links created yet</p>
              <p className="text-gray-400 text-sm">Create your first link to start earning!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {links.map((link) => (
                <div key={link.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <p className="text-white font-medium truncate mr-4">
                          {link.originalUrl}
                        </p>
                        <span className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded">
                          {link.shortCode}
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-300 space-x-4">
                        <span className="flex items-center">
                          <MousePointer className="w-4 h-4 mr-1" />
                          {link.clicks} clicks
                        </span>
                        <span className="flex items-center">
                          <DollarSign className="w-4 h-4 mr-1" />
                          ${(link.earnings || 0).toFixed(3)}
                        </span>
                        <span>{new Date(link.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => copyToClipboard(`${window.location.origin}/s/${link.shortCode}`)}
                        className="p-2 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-colors"
                        title="Copy link"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <a
                        href={`/s/${link.shortCode}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-purple-500/20 text-purple-300 rounded-lg hover:bg-purple-500/30 transition-colors"
                        title="Open link"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Withdraw Modal */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 w-full max-w-md border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4">Withdraw Money</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Amount (USD)
                </label>
                <input
                  type="number"
                  min="4"
                  max={user.availableBalance}
                  step="0.001"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  placeholder="Enter amount"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Available: ${user.availableBalance.toFixed(3)} | Minimum: $4.00
                </p>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowWithdrawModal(false)}
                  className="flex-1 px-4 py-3 bg-gray-500/20 text-gray-300 rounded-lg hover:bg-gray-500/30 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleWithdraw}
                  disabled={withdrawLoading || !withdrawAmount || parseFloat(withdrawAmount) < 4}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {withdrawLoading ? 'Processing...' : 'Withdraw'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Details Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 w-full max-w-2xl border border-white/20 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-white mb-6">Payment Details & Verification</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={paymentData.fullName}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, fullName: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={paymentData.phoneNumber}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="+1234567890"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Complete Address *
                </label>
                <textarea
                  value={paymentData.address}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  placeholder="Street, City, State, Country, Postal Code"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Telegram Username *
                  </label>
                  <input
                    type="text"
                    value={paymentData.telegramUsername}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, telegramUsername: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="@yourusername"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Payment Method *
                  </label>
                  <select
                    value={paymentData.paymentMethod}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-400 [&>option]:bg-gray-800 [&>option]:text-white"
                    required
                  >
                    <option value="" className="bg-gray-800 text-white">Select payment method</option>
                    <option value="upi" className="bg-gray-800 text-white">🇮🇳 UPI ID (India)</option>
                    <option value="paypal" className="bg-gray-800 text-white">💳 PayPal</option>
                    <option value="bank" className="bg-gray-800 text-white">🏦 Bank Transfer</option>
                    <option value="skrill" className="bg-gray-800 text-white">💰 Skrill</option>
                    <option value="other" className="bg-gray-800 text-white">🔧 Other (Contact Support)</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Payment Details *
                </label>
                {paymentData.paymentMethod === 'other' ? (
                  <div className="space-y-3">
                    <textarea
                      value={paymentData.paymentDetails}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, paymentDetails: e.target.value }))}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                      placeholder="Describe your preferred payment method (e.g., Crypto wallet, Mobile money, etc.)"
                      rows={3}
                      required
                    />
                    <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-3">
                      <p className="text-blue-200 text-sm">
                        📞 <strong>Contact Support:</strong> Since you selected "Other", please contact our support team at{' '}
                        <span className="font-mono bg-blue-500/30 px-1 rounded"><EMAIL></span> or{' '}
                        <span className="font-mono bg-blue-500/30 px-1 rounded">@SupportBot</span> on Telegram to discuss your payment method.
                      </p>
                    </div>
                  </div>
                ) : (
                  <input
                    type="text"
                    value={paymentData.paymentDetails}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, paymentDetails: e.target.value }))}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder={
                      paymentData.paymentMethod === 'upi' ? 'yourname@paytm (e.g., john@paytm, mary@phonepe)' :
                      paymentData.paymentMethod === 'paypal' ? '<EMAIL>' :
                      paymentData.paymentMethod === 'bank' ? 'Account Number, Bank Name, IFSC Code' :
                      paymentData.paymentMethod === 'skrill' ? '<EMAIL>' :
                      'Enter payment details'
                    }
                    required
                  />
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Traffic Source Description *
                </label>
                <textarea
                  value={paymentData.trafficSource}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, trafficSource: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  placeholder="Describe where you get traffic for your links (social media, websites, etc.)"
                  rows={2}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Traffic Source Links *
                </label>
                <textarea
                  value={paymentData.trafficSourceLinks}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, trafficSourceLinks: e.target.value }))}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  placeholder="Provide links to your channels/websites where you share links:&#10;• YouTube: https://youtube.com/@yourchannel&#10;• Instagram: https://instagram.com/yourpage&#10;• Website: https://yourwebsite.com&#10;• Telegram: https://t.me/yourchannel"
                  rows={4}
                  required
                />
                <p className="text-xs text-gray-400 mt-1">
                  📝 Provide actual links to verify your traffic sources (YouTube channels, Instagram pages, websites, etc.)
                </p>
              </div>

              <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4">
                <h4 className="text-blue-200 font-medium mb-2">📋 Verification Process</h4>
                <ul className="text-blue-300 text-sm space-y-1">
                  <li>• Admin will verify your information manually</li>
                  <li>• Ensure all details are accurate and complete</li>
                  <li>• Verification may take 24-48 hours</li>
                  <li>• You can withdraw once verified</li>
                  <li>• For "Other" payment methods, contact support first</li>
                </ul>
                <div className="mt-3 pt-3 border-t border-blue-500/30">
                  <p className="text-blue-200 text-sm">
                    📞 <strong>Need Help?</strong> Contact support at{' '}
                    <span className="font-mono bg-blue-500/30 px-1 rounded"><EMAIL></span>
                  </p>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="flex-1 px-4 py-3 bg-gray-500/20 text-gray-300 rounded-lg hover:bg-gray-500/30 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePaymentDetails}
                  disabled={paymentLoading || !paymentData.fullName || !paymentData.paymentMethod}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {paymentLoading ? 'Saving...' : 'Save Payment Details'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
