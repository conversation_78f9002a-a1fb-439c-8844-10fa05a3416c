import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertLinkSchema, type NewsArticle, type Deal } from "@shared/schema";
import { nanoid } from "nanoid";

function generateShortCode(): string {
  return nanoid(8);
}

function getNewsFromEnv(): NewsArticle[] {
  const news: NewsArticle[] = [];
  let index = 1;
  
  while (process.env[`NEWS_${index}`]) {
    try {
      const newsData = JSON.parse(process.env[`NEWS_${index}`] || '{}');
      news.push({
        id: `news_${index}`,
        title: newsData.title || `Gaming News ${index}`,
        excerpt: newsData.excerpt || 'Latest gaming news update',
        category: newsData.category || 'Gaming',
        date: newsData.date || 'Today',
        readTime: newsData.readTime || '3 min read',
        imageUrl: newsData.imageUrl || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400'
      });
      index++;
    } catch (e) {
      break;
    }
  }
  
  // Return empty array if no environment variables configured
  // Admin must set NEWS_1, NEWS_2, etc. in environment variables
  
  return news;
}

function getDealsFromEnv(): Deal[] {
  const deals: Deal[] = [];
  let index = 1;
  
  while (process.env[`DEAL_${index}`]) {
    try {
      const dealData = JSON.parse(process.env[`DEAL_${index}`] || '{}');
      deals.push({
        id: `deal_${index}`,
        title: dealData.title || `Gaming Deal ${index}`,
        currentPrice: dealData.currentPrice || '$99.99',
        originalPrice: dealData.originalPrice || '$149.99',
        discount: dealData.discount || '33% OFF',
        timeLeft: dealData.timeLeft || '2 days left',
        imageUrl: dealData.imageUrl || 'https://images.unsplash.com/photo-1616763355548-1b606f439f86?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300'
      });
      index++;
    } catch (e) {
      break;
    }
  }
  
  // Return empty array if no environment variables configured
  // Admin must set DEAL_1, DEAL_2, etc. in environment variables
  
  return deals;
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Database connection test endpoint
  app.get("/api/db/test", async (req, res) => {
    try {
      const { testDatabaseConnection } = await import("./db");
      const connected = await testDatabaseConnection();
      
      if (connected) {
        res.json({ status: "connected", message: "Database connection successful" });
      } else {
        res.status(500).json({ status: "failed", message: "Database connection failed" });
      }
    } catch (error) {
      console.error('Database test error:', error);
      res.status(500).json({ 
        status: "error", 
        message: "Database test failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Database schema push endpoint
  app.get("/api/db/push", async (req, res) => {
    try {
      const { execSync } = await import('child_process');
      console.log('Running database migrations...');
      
      const result = execSync('npm run db:push', { 
        encoding: 'utf8',
        cwd: process.cwd(),
        env: { ...process.env }
      });
      
      console.log('Migration result:', result);
      res.json({ 
        status: "success", 
        message: "Database schema updated successfully",
        output: result
      });
    } catch (error) {
      console.error('Migration error:', error);
      res.status(500).json({ 
        status: "error", 
        message: "Database migration failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Get news from environment variables
  app.get("/api/news", async (req, res) => {
    try {
      const news = getNewsFromEnv();
      res.json(news);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch news" });
    }
  });

  // Get deals from environment variables
  app.get("/api/deals", async (req, res) => {
    try {
      const deals = getDealsFromEnv();
      res.json(deals);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch deals" });
    }
  });

  // Create shortened link
  app.post("/api/links/shorten", async (req, res) => {
    try {
      // Log for debugging in production (remove after testing)
      if (process.env.NODE_ENV === 'production') {
        console.log('Production - Content-Type:', req.get('Content-Type'));
        console.log('Production - Body type:', typeof req.body);
        console.log('Production - Body:', JSON.stringify(req.body));
      }
      
      // Handle both JSON and form-encoded data
      let originalUrl: string;
      
      if (req.body && typeof req.body === 'object' && req.body.originalUrl) {
        originalUrl = req.body.originalUrl;
      } else if (typeof req.body === 'string') {
        // Handle case where body is received as string
        try {
          const parsed = JSON.parse(req.body);
          originalUrl = parsed.originalUrl;
        } catch (e) {
          return res.status(400).json({ message: "Invalid JSON format" });
        }
      } else {
        return res.status(400).json({ 
          message: "originalUrl is required",
          receivedBody: req.body,
          bodyType: typeof req.body
        });
      }
      
      if (!originalUrl || typeof originalUrl !== 'string') {
        return res.status(400).json({ 
          message: "originalUrl must be a non-empty string",
          received: originalUrl
        });
      }
      
      // Validate with Zod schema
      const { originalUrl: validatedUrl } = insertLinkSchema.parse({ originalUrl });
      
      // Validate URL format
      try {
        new URL(validatedUrl);
      } catch (e) {
        return res.status(400).json({ message: "Invalid URL format" });
      }

      const shortCode = generateShortCode();
      const link = await storage.createShortenedLink({ originalUrl: validatedUrl, shortCode });
      
      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get('host')}`;
      
      res.json({
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt
      });
    } catch (error) {
      console.error('Error in /api/links/shorten:', error);
      
      // Check if it's a database connection error
      if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
        return res.status(503).json({ 
          message: "Database connection failed. Please check database configuration.",
          details: "Unable to connect to the database. Ensure DATABASE_URL is correct and database is running."
        });
      }
      
      if (error instanceof Error) {
        return res.status(400).json({ 
          message: "Validation failed", 
          details: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
      }
      
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Redirect shortened link through multi-step process
  app.get("/s/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }

      // Redirect to first step of the process
      res.redirect(`/redirect-step1?code=${shortCode}`);
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get original link for final redirect (and track click)
  app.get("/api/links/original/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }

      // Track the click when user reaches final page
      await storage.updateLinkClicks(shortCode);
      
      res.json({
        originalUrl: link.originalUrl,
        shortCode: link.shortCode,
        clicks: link.clicks + 1
      });
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get recent links with analytics
  app.get("/api/links/recent", async (req, res) => {
    try {
      const links = await storage.getRecentLinks();
      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get('host')}`;
      
      const formattedLinks = links.map(link => ({
        id: link.id,
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt
      }));
      res.json(formattedLinks);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recent links" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
