import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertLinkSchema, withdrawalRequestSchema, paymentDetailsSchema, type NewsArticle, type Deal } from "@shared/schema";
import { nanoid } from "nanoid";
import { register, login, getProfile, authenticateToken, optionalAuth, type AuthRequest } from "./auth";
import { registrationBot } from "./telegram-bot";

function generateShortCode(): string {
  return nanoid(8);
}

function getNewsFromEnv(): NewsArticle[] {
  const news: NewsArticle[] = [];
  let index = 1;
  
  while (process.env[`NEWS_${index}`]) {
    try {
      const newsData = JSON.parse(process.env[`NEWS_${index}`] || '{}');
      news.push({
        id: `news_${index}`,
        title: newsData.title || `Gaming News ${index}`,
        excerpt: newsData.excerpt || 'Latest gaming news update',
        category: newsData.category || 'Gaming',
        date: newsData.date || 'Today',
        readTime: newsData.readTime || '3 min read',
        imageUrl: newsData.imageUrl || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400'
      });
      index++;
    } catch (e) {
      break;
    }
  }
  
  // Return empty array if no environment variables configured
  // Admin must set NEWS_1, NEWS_2, etc. in environment variables
  
  return news;
}

function getDealsFromEnv(): Deal[] {
  const deals: Deal[] = [];
  let index = 1;
  
  while (process.env[`DEAL_${index}`]) {
    try {
      const dealData = JSON.parse(process.env[`DEAL_${index}`] || '{}');
      deals.push({
        id: `deal_${index}`,
        title: dealData.title || `Gaming Deal ${index}`,
        currentPrice: dealData.currentPrice || '$99.99',
        originalPrice: dealData.originalPrice || '$149.99',
        discount: dealData.discount || '33% OFF',
        timeLeft: dealData.timeLeft || '2 days left',
        imageUrl: dealData.imageUrl || 'https://images.unsplash.com/photo-1616763355548-1b606f439f86?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300'
      });
      index++;
    } catch (e) {
      break;
    }
  }
  
  // Return empty array if no environment variables configured
  // Admin must set DEAL_1, DEAL_2, etc. in environment variables
  
  return deals;
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Telegram storage health check endpoint
  app.get("/api/storage/test", async (req, res) => {
    try {
      // Test if Telegram bot is configured correctly
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      const channelId = process.env.TELEGRAM_CHANNEL_ID;

      if (!botToken || !channelId) {
        return res.status(500).json({
          status: "error",
          message: "Telegram bot not configured. Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHANNEL_ID"
        });
      }

      res.json({
        status: "configured",
        message: "Telegram storage is configured and ready",
        botConfigured: !!botToken,
        channelConfigured: !!channelId
      });
    } catch (error) {
      console.error('Telegram storage test error:', error);
      res.status(500).json({
        status: "error",
        message: "Telegram storage test failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Authentication routes
  app.post("/api/auth/register", register);
  app.post("/api/auth/login", login);
  app.get("/api/auth/profile", authenticateToken, getProfile);

  // User dashboard routes
  app.get("/api/user/links", authenticateToken, async (req: AuthRequest, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const links = await storage.getUserLinks(req.user.id);
      res.json({ links });
    } catch (error) {
      console.error('Error fetching user links:', error);
      res.status(500).json({ message: "Failed to fetch links" });
    }
  });

  app.get("/api/user/withdrawals", authenticateToken, async (req: AuthRequest, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const withdrawals = await storage.getUserWithdrawals(req.user.id);
      res.json({ withdrawals });
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      res.status(500).json({ message: "Failed to fetch withdrawals" });
    }
  });

  app.post("/api/user/payment-details", authenticateToken, async (req: AuthRequest, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const validation = paymentDetailsSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          message: "Validation failed",
          errors: validation.error.errors
        });
      }

      const paymentData = validation.data;
      await storage.updateUserPaymentDetails(req.user.id, paymentData);

      res.json({
        message: "Payment details saved successfully"
      });
    } catch (error) {
      console.error('Error saving payment details:', error);
      res.status(500).json({
        message: error instanceof Error ? error.message : "Failed to save payment details"
      });
    }
  });

  app.post("/api/user/withdraw", authenticateToken, async (req: AuthRequest, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const validation = withdrawalRequestSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          message: "Validation failed",
          errors: validation.error.errors
        });
      }

      const { amount } = validation.data;

      // Get user details (payment details are optional)
      const user = await storage.getUserById(req.user.id);
      if (!user) {
        return res.status(404).json({
          message: "User not found"
        });
      }

      const withdrawal = await storage.createWithdrawalRequest(req.user.id, amount, user.paymentMethod || 'not_set');

      res.json({
        message: "Withdrawal request submitted successfully",
        withdrawal
      });
    } catch (error) {
      console.error('Error creating withdrawal:', error);
      res.status(500).json({
        message: error instanceof Error ? error.message : "Failed to create withdrawal request"
      });
    }
  });

  // Test endpoint to set user balance (for testing only)
  app.post("/api/test/set-balance", authenticateToken, async (req: AuthRequest, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { balance } = req.body;
      if (typeof balance !== 'number' || balance < 0) {
        return res.status(400).json({ message: "Invalid balance amount" });
      }

      await storage.setUserBalanceForTesting(req.user.id, balance);
      res.json({ message: `Balance set to $${balance.toFixed(3)} for testing` });
    } catch (error) {
      console.error('Error setting test balance:', error);
      res.status(500).json({ message: "Failed to set balance" });
    }
  });

  // Manual link recovery endpoint
  app.post("/api/links/recover", async (req, res) => {
    try {
      const { shortCode, originalUrl } = req.body;

      if (!shortCode || !originalUrl) {
        return res.status(400).json({
          message: "Missing required fields: shortCode and originalUrl"
        });
      }

      // Manually recreate the link in cache
      const link = {
        id: Date.now(),
        originalUrl: originalUrl,
        shortCode: shortCode,
        clicks: 0,
        createdAt: new Date()
      };

      // Add to storage cache (this will make the link work again)
      const storageInstance = storage.instance as any;
      storageInstance.cache.set(shortCode, link);

      console.log(`Manually recovered link: ${shortCode} -> ${originalUrl}`);

      res.json({
        message: "Link recovered successfully",
        link: link,
        shortUrl: `${req.protocol}://${req.get('host')}/s/${shortCode}`
      });
    } catch (error) {
      console.error('Error recovering link:', error);
      res.status(500).json({ message: "Failed to recover link" });
    }
  });

  // Get news from environment variables
  app.get("/api/news", async (req, res) => {
    try {
      const news = getNewsFromEnv();
      res.json(news);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch news" });
    }
  });

  // Get deals from environment variables
  app.get("/api/deals", async (req, res) => {
    try {
      const deals = getDealsFromEnv();
      res.json(deals);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch deals" });
    }
  });

  // Create shortened link (with optional authentication)
  app.post("/api/links/shorten", optionalAuth, async (req: AuthRequest, res) => {
    try {
      // Log for debugging in production (remove after testing)
      if (process.env.NODE_ENV === 'production') {
        console.log('Production - Content-Type:', req.get('Content-Type'));
        console.log('Production - Body type:', typeof req.body);
        console.log('Production - Body:', JSON.stringify(req.body));
      }
      
      // Handle both JSON and form-encoded data
      let originalUrl: string;
      
      if (req.body && typeof req.body === 'object' && req.body.originalUrl) {
        originalUrl = req.body.originalUrl;
      } else if (typeof req.body === 'string') {
        // Handle case where body is received as string
        try {
          const parsed = JSON.parse(req.body);
          originalUrl = parsed.originalUrl;
        } catch (e) {
          return res.status(400).json({ message: "Invalid JSON format" });
        }
      } else {
        return res.status(400).json({ 
          message: "originalUrl is required",
          receivedBody: req.body,
          bodyType: typeof req.body
        });
      }
      
      if (!originalUrl || typeof originalUrl !== 'string') {
        return res.status(400).json({ 
          message: "originalUrl must be a non-empty string",
          received: originalUrl
        });
      }
      
      // Validate with Zod schema
      const { originalUrl: validatedUrl } = insertLinkSchema.parse({ originalUrl });
      
      // Validate URL format
      try {
        new URL(validatedUrl);
      } catch (e) {
        return res.status(400).json({ message: "Invalid URL format" });
      }

      const shortCode = generateShortCode();
      const userId = req.user?.id; // Get user ID if authenticated

      const link = await storage.createShortenedLink({
        originalUrl: validatedUrl,
        shortCode,
        userId
      });

      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get('host')}`;

      // If user is authenticated, update their link count
      if (userId) {
        const user = await storage.getUserById(userId);
        if (user) {
          user.totalLinks += 1;
          console.log(`User ${user.username} created link: ${shortCode}`);
        }
      }

      res.json({
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt,
        isUserLink: !!userId,
        earnings: link.earnings || 0
      });
    } catch (error) {
      console.error('Error in /api/links/shorten:', error);
      
      // Check if it's a Telegram API error
      if (error instanceof Error && (error.message.includes('TELEGRAM') || error.message.includes('bot'))) {
        return res.status(503).json({
          message: "Telegram storage failed. Please check bot configuration.",
          details: "Unable to save to Telegram. Ensure TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID are correct."
        });
      }
      
      if (error instanceof Error) {
        return res.status(400).json({ 
          message: "Validation failed", 
          details: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
      }
      
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Redirect shortened link through multi-step process
  app.get("/s/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }

      // Redirect to first step of the process
      res.redirect(`/redirect-step1?code=${shortCode}`);
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get original link for final redirect (and track click)
  app.get("/api/links/original/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }

      // Track the click when user reaches final page
      // Pass the link's userId to track earnings
      await storage.updateLinkClicks(shortCode, link.userId);
      
      res.json({
        originalUrl: link.originalUrl,
        shortCode: link.shortCode,
        clicks: link.clicks + 1
      });
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get recent links with analytics
  app.get("/api/links/recent", async (req, res) => {
    try {
      const links = await storage.getRecentLinks();
      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get('host')}`;
      
      const formattedLinks = links.map(link => ({
        id: link.id,
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt
      }));
      res.json(formattedLinks);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recent links" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
