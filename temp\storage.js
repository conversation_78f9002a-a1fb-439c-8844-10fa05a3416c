import TelegramBot from 'node-telegram-bot-api';
export class TelegramStorage {
    constructor() {
        this.cache = new Map();
        this.initialized = false;
        const botToken = process.env.TELEGRAM_BOT_TOKEN;
        const channelId = process.env.TELEGRAM_CHANNEL_ID;
        if (!botToken || !channelId) {
            throw new Error('TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID must be set');
        }
        this.bot = new TelegramBot(botToken);
        this.channelId = channelId;
    }
    async initializeCache() {
        if (this.initialized)
            return;
        try {
            // Load existing links from Telegram channel
            await this.loadLinksFromChannel();
            this.initialized = true;
        }
        catch (error) {
            console.error('Failed to initialize Telegram storage cache:', error);
            // Continue without cache - new links will still work
            this.initialized = true;
        }
    }
    async loadLinksFromChannel() {
        try {
            // Get recent messages from the channel
            // Note: This is a simplified approach. In production, you might want to use a more robust method
            console.log('Loading existing links from Telegram channel...');
            // For now, we'll start with an empty cache and build it as we go
        }
        catch (error) {
            console.error('Error loading links from channel:', error);
        }
    }
    async createShortenedLink(linkData) {
        await this.initializeCache();
        const link = {
            id: Date.now(), // Use timestamp as ID
            originalUrl: linkData.originalUrl,
            shortCode: linkData.shortCode,
            clicks: 0,
            createdAt: new Date(),
        };
        try {
            // Save to Telegram channel
            const message = JSON.stringify({
                type: 'shortened_link',
                data: link
            });
            await this.bot.sendMessage(this.channelId, message);
            // Cache the link
            this.cache.set(linkData.shortCode, link);
            console.log(`Saved link to Telegram: ${linkData.shortCode} -> ${linkData.originalUrl}`);
            return link;
        }
        catch (error) {
            console.error('Error saving link to Telegram:', error);
            throw new Error('Failed to save shortened link');
        }
    }
    async getShortenedLink(shortCode) {
        await this.initializeCache();
        // First check cache
        const cachedLink = this.cache.get(shortCode);
        if (cachedLink) {
            return cachedLink;
        }
        // If not in cache, it might be an old link from before cache initialization
        // For now, return undefined. In a production system, you might want to search through channel history
        return undefined;
    }
    async updateLinkClicks(shortCode) {
        await this.initializeCache();
        const link = this.cache.get(shortCode);
        if (link) {
            link.clicks += 1;
            try {
                // Send updated click count to Telegram
                const message = JSON.stringify({
                    type: 'click_update',
                    shortCode: shortCode,
                    clicks: link.clicks,
                    timestamp: new Date().toISOString()
                });
                await this.bot.sendMessage(this.channelId, message);
            }
            catch (error) {
                console.error('Error updating clicks in Telegram:', error);
                // Don't throw error - click tracking is not critical
            }
        }
    }
    async getRecentLinks() {
        await this.initializeCache();
        // Return recent links from cache, sorted by creation date
        const links = Array.from(this.cache.values())
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(0, 10);
        return links;
    }
}
// Lazy initialization to ensure environment variables are loaded
let _storage = null;
export const storage = {
    get instance() {
        if (!_storage) {
            _storage = new TelegramStorage();
        }
        return _storage;
    },
    // Proxy all methods to the instance
    async createShortenedLink(link) {
        return this.instance.createShortenedLink(link);
    },
    async getShortenedLink(shortCode) {
        return this.instance.getShortenedLink(shortCode);
    },
    async updateLinkClicks(shortCode) {
        return this.instance.updateLinkClicks(shortCode);
    },
    async getRecentLinks() {
        return this.instance.getRecentLinks();
    }
};
