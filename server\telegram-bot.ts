import TelegramBot from 'node-telegram-bot-api';
import { storage } from './storage';

// Test bot configuration from environment
const TEST_BOT_TOKEN = process.env.TEST_BOT_TOKEN || '7817954175:AAGICw_FASiks6345fDRW-1d0oeGb0jbdfM';
const TEST_CHANNEL_ID = process.env.TEST_CHANNEL_ID || '-1002795432631';

console.log('🔧 Bot configuration:');
console.log('📱 TEST_BOT_TOKEN:', TEST_BOT_TOKEN ? 'Set' : 'Missing');
console.log('📺 TEST_CHANNEL_ID:', TEST_CHANNEL_ID);

export class UserRegistrationBot {
  private bot: TelegramBot;
  private userSessions: Map<string, any> = new Map();
  private completedRegistrations: Set<string> = new Set(); // Track completed registrations

  constructor() {
    try {
      this.bot = new TelegramBot(TEST_BOT_TOKEN, {
        polling: {
          interval: 1000,
          autoStart: true,
          params: {
            timeout: 10
          }
        }
      });

      this.setupHandlers();
      this.setupErrorHandlers();

      console.log('🤖 User Registration Bot started with polling');
      console.log('📱 Bot username should be: @DileVdbot');
      console.log('🔗 Users should message: https://t.me/DileVdbot');

      // Test bot connection
      this.testBotConnection();
    } catch (error) {
      console.error('❌ Failed to initialize registration bot:', error);
    }
  }

  private async testBotConnection() {
    try {
      const me = await this.bot.getMe();
      console.log(`✅ Bot connected successfully: @${me.username} (${me.first_name})`);
    } catch (error) {
      console.error('❌ Bot connection test failed:', error);
    }
  }

  private setupErrorHandlers() {
    this.bot.on('polling_error', (error) => {
      console.error('🔴 Telegram polling error:', error.message);
    });

    this.bot.on('error', (error) => {
      console.error('🔴 Telegram bot error:', error.message);
    });
  }

  private setupHandlers() {
    console.log('🔧 Setting up bot message handlers...');

    // Start command
    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`📱 /start command received from @${username} (ID: ${telegramId})`);

      if (!telegramId) {
        console.log('❌ No telegram ID found in message');
        return;
      }

      // Check if user already completed registration
      if (this.completedRegistrations.has(telegramId)) {
        console.log(`✅ User ${username} already registered, showing main menu`);
        this.showMainMenu(chatId, username);
        return;
      }

      console.log(`💬 Sending welcome message to chat ${chatId}`);

      this.bot.sendMessage(chatId,
        `🎉 Welcome to Gaming Deals Tracker Earnings Program!\n\n` +
        `To start earning money from your shortened links, I need to collect some information.\n\n` +
        `Please provide your mobile number (with country code):\n` +
        `Example: +**********`
      ).then(() => {
        console.log(`✅ Welcome message sent successfully to ${username}`);
      }).catch((error) => {
        console.error(`❌ Failed to send welcome message:`, error.message);
      });

      // Initialize user session
      this.userSessions.set(telegramId, {
        step: 'mobile',
        telegramId: telegramId,
        chatId: chatId,
        username: username
      });

      console.log(`📝 Session created for user ${username} (${telegramId})`);
    });

    // Handle text messages
    this.bot.on('message', async (msg) => {
      if (msg.text?.startsWith('/')) return; // Skip commands

      const telegramId = msg.from?.id.toString();
      const chatId = msg.chat.id;
      const username = msg.from?.username || 'Unknown';

      console.log(`💬 Message received from @${username}: "${msg.text}"`);

      if (!telegramId || !msg.text) {
        console.log('❌ Missing telegram ID or message text');
        return;
      }

      const session = this.userSessions.get(telegramId);
      if (!session) {
        console.log(`❌ No session found for user ${username}, sending start prompt`);
        this.bot.sendMessage(chatId, 'Please start with /start command');
        return;
      }

      console.log(`🔄 Processing message for user ${username} in step: ${session.step}`);
      await this.handleUserInput(telegramId, msg.text, chatId);
    });

    // Handle callback queries (inline keyboard buttons)
    this.bot.on('callback_query', async (query) => {
      const telegramId = query.from.id.toString();
      const chatId = query.message?.chat.id;
      const username = query.from.username || 'Unknown';

      if (!chatId) return;

      console.log(`🔘 Callback query from @${username}: ${query.data}`);

      if (query.data?.startsWith('menu_')) {
        await this.handleMenuCallback(telegramId, query.data, chatId, username);
      } else {
        await this.handleCallbackQuery(telegramId, query.data || '', chatId);
      }

      this.bot.answerCallbackQuery(query.id);
    });
  }

  private async handleUserInput(telegramId: string, text: string, chatId: number) {
    const session = this.userSessions.get(telegramId);
    if (!session) return;

    switch (session.step) {
      case 'awaiting_url':
        await this.handleUrlShortening(telegramId, text, chatId);
        break;
      case 'mobile':
        // Validate mobile number
        if (!/^\+\d{10,15}$/.test(text)) {
          this.bot.sendMessage(chatId, 
            '❌ Invalid mobile number format. Please provide a valid number with country code.\n' +
            'Example: +**********'
          );
          return;
        }

        session.mobileNumber = text;
        session.step = 'withdrawal';

        // Show withdrawal method options
        const withdrawalKeyboard = {
          inline_keyboard: [
            [{ text: '🇮🇳 UPI ID', callback_data: 'withdrawal_upi' }],
            [{ text: '💳 PayPal', callback_data: 'withdrawal_paypal' }],
            [{ text: '🏦 Bank Transfer', callback_data: 'withdrawal_bank' }],
            [{ text: '💰 Skrill', callback_data: 'withdrawal_skrill' }]
          ]
        };

        this.bot.sendMessage(chatId, 
          `✅ Mobile number saved: ${text}\n\n` +
          `Now, please select your preferred withdrawal method:`,
          { reply_markup: withdrawalKeyboard }
        );
        break;

      case 'withdrawal_details':
        session.withdrawalDetails = text;
        await this.completeRegistration(telegramId, chatId);
        break;
    }

    this.userSessions.set(telegramId, session);
  }

  private async handleCallbackQuery(telegramId: string, data: string, chatId: number) {
    const session = this.userSessions.get(telegramId);

    if (data === 'menu_back') {
      // Clear any active session and show main menu
      this.userSessions.delete(telegramId);
      const username = 'User'; // We could store this better
      this.showMainMenu(chatId, username);
      return;
    }

    if (!session) return;

    if (data.startsWith('withdrawal_')) {
      const method = data.replace('withdrawal_', '');
      session.withdrawalMethod = method;
      session.step = 'withdrawal_details';

      let promptText = '';
      switch (method) {
        case 'upi':
          promptText = '🇮🇳 Please provide your UPI ID (e.g., yourname@paytm):';
          break;
        case 'paypal':
          promptText = '💳 Please provide your PayPal email address:';
          break;
        case 'bank':
          promptText = '🏦 Please provide your bank account details (Account Number, Bank Name, IFSC):';
          break;
        case 'skrill':
          promptText = '💰 Please provide your Skrill email address:';
          break;
      }

      this.bot.sendMessage(chatId, promptText);
      this.userSessions.set(telegramId, session);
    }
  }

  private async handleUrlShortening(telegramId: string, url: string, chatId: number) {
    try {
      // Validate URL
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        this.bot.sendMessage(chatId,
          '❌ Invalid URL format. Please provide a valid URL starting with http:// or https://'
        );
        return;
      }

      // Call the API to shorten the link
      const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
      const response = await fetch(`${baseUrl}/api/links/shorten`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ originalUrl: url })
      });

      if (response.ok) {
        const data = await response.json();

        const successKeyboard = {
          inline_keyboard: [
            [{ text: '🔗 Shorten Another', callback_data: 'menu_shorten' }],
            [{ text: '🔙 Back to Menu', callback_data: 'menu_back' }]
          ]
        };

        this.bot.sendMessage(chatId,
          `✅ Link shortened successfully!\n\n` +
          `🔗 Short URL: ${data.shortUrl}\n` +
          `📊 Original: ${data.originalUrl}\n\n` +
          `💡 Share this link to start earning $0.007 per click!`,
          { reply_markup: successKeyboard }
        );
      } else {
        this.bot.sendMessage(chatId,
          '❌ Failed to shorten link. Please try again or contact support.'
        );
      }

      // Clear the session
      this.userSessions.delete(telegramId);

    } catch (error) {
      console.error('Error shortening URL:', error);
      this.bot.sendMessage(chatId,
        '❌ Error shortening link. Please try again later.'
      );
      this.userSessions.delete(telegramId);
    }
  }

  private async completeRegistration(telegramId: string, chatId: number) {
    const session = this.userSessions.get(telegramId);
    if (!session) return;

    try {
      // Save registration data to test channel
      const registrationData = {
        type: 'telegram_registration',
        telegramId: session.telegramId,
        mobileNumber: session.mobileNumber,
        withdrawalMethod: session.withdrawalMethod,
        withdrawalDetails: session.withdrawalDetails,
        registeredAt: new Date().toISOString()
      };

      // Send to test channel
      await this.bot.sendMessage(TEST_CHANNEL_ID, JSON.stringify(registrationData, null, 2));

      // Send success message to user
      this.bot.sendMessage(chatId, 
        `🎉 Registration completed successfully!\n\n` +
        `📱 Mobile: ${session.mobileNumber}\n` +
        `💳 Withdrawal: ${session.withdrawalMethod}\n` +
        `📝 Details: ${session.withdrawalDetails}\n\n` +
        `✅ Your account is now set up for earning!\n\n` +
        `💰 You'll earn $0.007 for each click on your shortened links\n` +
        `💵 Minimum withdrawal: $4.00\n\n` +
        `🔗 Start creating links at: ${process.env.BASE_URL || 'your-app-url'}\n\n` +
        `Use /dashboard to check your earnings anytime!\n\n` +
        `📱 Bot: @DileVdbot`
      );

      // Mark registration as completed
      this.completedRegistrations.add(telegramId);

      // Clean up session
      this.userSessions.delete(telegramId);

      console.log(`✅ Telegram registration completed for user ${telegramId}`);
    } catch (error) {
      console.error('Error completing registration:', error);
      this.bot.sendMessage(chatId,
        '❌ Registration failed. Please try again later or contact support.'
      );
    }
  }

  private showMainMenu(chatId: number, username: string) {
    const menuKeyboard = {
      inline_keyboard: [
        [{ text: '📊 Dashboard', callback_data: 'menu_dashboard' }],
        [{ text: '🔗 Shorten Link', callback_data: 'menu_shorten' }],
        [{ text: '🏠 Go to Website', url: process.env.BASE_URL || 'http://localhost:5000' }]
      ]
    };

    this.bot.sendMessage(chatId,
      `🎉 Welcome back, ${username}!\n\n` +
      `Your registration is complete. What would you like to do?`,
      { reply_markup: menuKeyboard }
    );
  }

  private async handleMenuCallback(telegramId: string, data: string, chatId: number, username: string) {
    switch (data) {
      case 'menu_dashboard':
        await this.showDashboard(chatId, telegramId);
        break;
      case 'menu_shorten':
        await this.startLinkShortening(chatId, telegramId);
        break;
    }
  }

  private async showDashboard(chatId: number, telegramId: string) {
    // This would typically fetch real user data from storage
    // For now, show placeholder data
    const backKeyboard = {
      inline_keyboard: [
        [{ text: '🔙 Back to Menu', callback_data: 'menu_back' }],
        [{ text: '🏠 Go to Website', url: process.env.BASE_URL || 'http://localhost:5000' }]
      ]
    };

    this.bot.sendMessage(chatId,
      `📊 Your Earnings Dashboard\n\n` +
      `💰 Total Earnings: $0.000\n` +
      `💵 Available Balance: $0.000\n` +
      `🔗 Total Links: 0\n` +
      `👆 Total Clicks: 0\n\n` +
      `💡 Create your first link on the website to start earning!\n` +
      `🎯 Earn $0.007 per click • Minimum withdrawal: $4.00`,
      { reply_markup: backKeyboard }
    );
  }

  private async startLinkShortening(chatId: number, telegramId: string) {
    // Set user in link shortening mode
    this.userSessions.set(telegramId, {
      step: 'awaiting_url',
      telegramId: telegramId,
      chatId: chatId
    });

    const cancelKeyboard = {
      inline_keyboard: [
        [{ text: '❌ Cancel', callback_data: 'menu_back' }]
      ]
    };

    this.bot.sendMessage(chatId,
      `🔗 Link Shortener\n\n` +
      `Please send me the URL you want to shorten:\n` +
      `Example: https://example.com`,
      { reply_markup: cancelKeyboard }
    );
  }

  // Dashboard command
  setupDashboardCommand() {
    this.bot.onText(/\/dashboard/, async (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      
      if (!telegramId) return;

      // This would typically fetch user data from storage
      // For now, show a placeholder message
      this.bot.sendMessage(chatId, 
        `📊 Your Earnings Dashboard\n\n` +
        `💰 Total Earnings: $0.000\n` +
        `💵 Available Balance: $0.000\n` +
        `🔗 Total Links: 0\n` +
        `👆 Total Clicks: 0\n\n` +
        `Create your first link at: ${process.env.BASE_URL || 'your-app-url'}`
      );
    });
  }

  // Method to send earnings update to user
  async notifyEarningsUpdate(telegramId: string, earnings: number, totalEarnings: number) {
    try {
      this.bot.sendMessage(parseInt(telegramId), 
        `💰 New Earnings!\n\n` +
        `+$${earnings.toFixed(3)} from a link click\n` +
        `Total Earnings: $${totalEarnings.toFixed(3)}\n\n` +
        `Keep sharing your links to earn more! 🚀`
      );
    } catch (error) {
      console.error('Error sending earnings notification:', error);
    }
  }
}

// Export singleton instance
export const registrationBot = new UserRegistrationBot();
